import { Navigate, useRoutes } from 'react-router-dom';

import { CONFIG } from 'src/config-global';

import { SplashScreen } from 'src/components/loading-screen';

import { useAuthContext } from 'src/auth/hooks';

import { authRoutes } from './auth';
import { mainRoutes } from './main';
import { dashboardRoutes } from './dashboard';

function RootRedirect() {
  const { authenticated, loading } = useAuthContext();

  if (loading) {
    return <SplashScreen />;
  }

  if (authenticated) {
    return <Navigate to={CONFIG.site.redirectPath} replace />;
  }

  return <Navigate to="/auth/jwt/sign-in" replace />;
}

export function Router() {
  return useRoutes([
    {
      path: '/',
      element: <RootRedirect />,
    },

    // Auth
    ...authRoutes,

    // Dashboard
    ...dashboardRoutes,

    // Main
    ...mainRoutes,

    // No match
    { path: '*', element: <Navigate to="/404" replace /> },
  ]);
}
