import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { DashboardLayout } from 'src/layouts/dashboard';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

// Index
const IndexPage = lazy(() => import('src/pages/dashboard'));
// Love
const LoveList = lazy(() => import('src/pages/dashboard/love/list'));
// User
const UserListPage = lazy(() => import('src/pages/dashboard/user/list'));
// Account
const AccountList = lazy(() => import('src/pages/dashboard/account/list'));
// Game
const GameList = lazy(() => import('src/pages/dashboard/game/list'));
// Carousel
const CarouselList = lazy(() => import('src/pages/dashboard/carousel/list'));

// ----------------------------------------------------------------------

const layoutContent = (
  <DashboardLayout>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayout>
);

export const dashboardRoutes = [
  {
    path: 'dashboard',
    element: <AuthGuard>{layoutContent}</AuthGuard>,
    children: [
      { element: <IndexPage />, index: true },
      {
        path: 'love',
        children: [{ path: 'list', element: <LoveList /> }],
      },
      {
        path: 'user',
        children: [{ path: 'list', element: <UserListPage /> }],
      },
      {
        path: 'account',
        children: [{ path: 'list', element: <AccountList /> }],
      },
      {
        path: 'game',
        children: [{ path: 'list', element: <GameList /> }],
      },
      {
        path: 'carousel',
        children: [{ path: 'list', element: <CarouselList /> }],
      },
    ],
  },
];
