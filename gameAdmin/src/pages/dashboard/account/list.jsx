import md5 from 'md5';
import GdxHelmet from 'src/components/gdx/gdx-data/components/GdxHelmet';
import GdxInput from 'src/components/gdx/gdx-data/components/GdxInput';
import { useLangTranslate } from 'src/components/gdx/gdx-data/context/common';
import GdxFormItem from 'src/components/gdx/gdx-form/components/GdxFormItem';
import RemoteTable from 'src/components/gdx/remote-table';
import { useRemoteTableContext } from 'src/components/gdx/remote-table/context/hook';

export default function Page() {
  return (
    <>
      <GdxHelmet title="账户管理" />

      <RemoteTable
        options={{
          defaultPageSize: 10,
          baseUrl: '/api/admin/account',
          topActions: ['new'],
          actions: ['edit', 'delete'],
          onPersist: (model) => ({
            ...model,
            password: md5(model.password),
          }),
          filters: [
            { id: 'id', label: 'ID', type: 'text' },
            { id: 'username', label: '用户名', type: 'text' },
          ],
          columns: [
            { id: 'id', name: 'ID', align: 'center' },
            { id: 'username', name: '用户名' },
          ],
          editForm: <EditFormItems></EditFormItems>,
        }}
      />
    </>
  );
}

function EditFormItems() {
  const { rl } = useLangTranslate();
  const { editRecord } = useRemoteTableContext();
  return (
    <>
      <GdxFormItem name="username">
        <GdxInput label={rl('Username')}></GdxInput>
      </GdxFormItem>
      <GdxFormItem name="password">
        <GdxInput label={rl('password')}></GdxInput>
      </GdxFormItem>
    </>
  );
}
