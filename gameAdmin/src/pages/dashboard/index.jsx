import { Stack } from '@mui/material';
import { useAuthContext } from 'src/auth/hooks';
import GdxHelmet from 'src/components/gdx/gdx-data/components/GdxHelmet';
import YxLoveFinishCards from 'src/components/yx-components/YxLoveFinishCards';
import YxMeetCountdown from 'src/components/yx-components/YxMeetCountdown';

export default function OverviewAppPage() {
  const { user } = useAuthContext();
  const { username } = user || {};
  return (
    <>
      <GdxHelmet title="首页" />
      {username === 'admin' && (
        <Stack spacing={2}>
          <YxMeetCountdown />
          <YxLoveFinishCards />
        </Stack>
      )}
    </>
  );
}
