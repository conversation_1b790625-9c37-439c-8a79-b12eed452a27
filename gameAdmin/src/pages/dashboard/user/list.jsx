import GdxHelmet from 'src/components/gdx/gdx-data/components/GdxHelmet';
import RemoteTable from 'src/components/gdx/remote-table';

export default function Page() {
  return (
    <>
      <GdxHelmet title="用户列表" />

      <RemoteTable
        options={{
          defaultPageSize: 10,
          baseUrl: '/api/admin/user',
          filters: [
            { id: 'id', label: 'ID', type: 'text' },
            { id: 'username', label: '用户名', type: 'text' },
          ],
          columns: [
            { id: 'id', name: 'ID', align: 'center' },
            { id: 'username', name: '用户名' },
          ],
        }}
      />
    </>
  );
}
