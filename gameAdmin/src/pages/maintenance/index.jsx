import { Box, Button, Typography } from '@mui/material';
import { MaintenanceIllustration } from 'src/assets/illustrations';
import { RouterLink } from 'src/routes/components';

import GdxHelmet from 'src/components/gdx/gdx-data/components/GdxHelmet';

export default function Page() {
  return (
    <>
      <GdxHelmet title="Maintenance" />

      <MaintenanceView />
    </>
  );
}

function MaintenanceView() {
  return (
    <Box display="flex" alignItems="center" flexDirection="column">
      <Typography variant="h3" sx={{ mb: 2 }}>
        Website currently under maintenance
      </Typography>

      <Typography sx={{ color: 'text.secondary' }}>
        We are currently working hard on this page!
      </Typography>

      <MaintenanceIllustration sx={{ my: { xs: 5, sm: 10 } }} />

      <Button component={RouterLink} href="/" size="large" variant="contained">
        Go to home
      </Button>
    </Box>
  );
}
