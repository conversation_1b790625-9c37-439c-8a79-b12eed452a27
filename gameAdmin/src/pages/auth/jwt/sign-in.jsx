import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useAuthContext } from 'src/auth/hooks';
import { z as zod } from 'zod';
import { useBoolean } from 'src/hooks/use-boolean';
import { signInWithPassword } from 'src/auth/context/jwt';
import { AnimateLogo2 } from 'src/components/animate';
import { Divider, IconButton, InputAdornment, Stack, Typography } from '@mui/material';
import { Field, Form } from 'src/components/hook-form';
import { Iconify, SocialIcon } from 'src/components/iconify';
import { LoadingButton } from '@mui/lab';
import GdxHelmet from 'src/components/gdx/gdx-data/components/GdxHelmet';

export default function Page() {
  return (
    <>
      <GdxHelmet title="后台登录" />

      <SignInView />
    </>
  );
}

const SignInSchema = zod.object({
  username: zod.string().min(1, { message: '请输入账户!' }),
  password: zod.string().min(1, { message: '请输入密码!' }).min(6, { message: '密码最少6位数!' }),
});

function SignInView() {
  const password = useBoolean();

  const defaultValues = { email: '', password: '' };

  const { checkUserSession } = useAuthContext();

  const methods = useForm({
    resolver: zodResolver(SignInSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      const resp = await signInWithPassword({ username: data.username, password: data.password });
      if (resp.status === 0) {
        checkUserSession?.();
        window.location.replace('/dashboard');
      }
    } catch (error) {
      console.error(error);
    }
  });

  const renderLogo = <AnimateLogo2 sx={{ mb: 3, mx: 'auto' }} />;

  const renderHead = (
    <Stack alignItems="center" spacing={1.5} sx={{ mb: 5 }}>
      <Typography variant="h5">登录您的帐户</Typography>

      {/* <Stack direction="row" spacing={0.5}>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          没有帐户？
        </Typography>

        <Link component={RouterLink} href={paths.authDemo.centered.signUp} variant="subtitle2">
          创建账户
        </Link>
      </Stack> */}
    </Stack>
  );

  const renderForm = (
    <Stack spacing={3}>
      <Field.Text
        name="username"
        label="账户"
        placeholder="请输入账户"
        InputLabelProps={{ shrink: true }}
      />

      <Stack spacing={1.5}>
        {/* <Link
          component={RouterLink}
          href={paths.authDemo.centered.resetPassword}
          variant="body2"
          color="inherit"
          sx={{ alignSelf: 'flex-end' }}
        >
          忘记密码?
        </Link> */}

        <Field.Text
          name="password"
          label="密码"
          placeholder="请输入密码"
          type={password.value ? 'text' : 'password'}
          InputLabelProps={{ shrink: true }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={password.onToggle} edge="end">
                  <Iconify icon={password.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      </Stack>

      <LoadingButton
        fullWidth
        color="inherit"
        size="large"
        type="submit"
        variant="contained"
        loading={isSubmitting}
        loadingIndicator="Sign in..."
      >
        登录
      </LoadingButton>
    </Stack>
  );

  const renderSignInWithSocials = (
    <>
      <Divider
        sx={{
          my: 3,
          typography: 'overline',
          color: 'text.disabled',
          '&::before, :after': { borderTopStyle: 'dashed' },
        }}
      >
        OR
      </Divider>

      <Stack direction="row" justifyContent="center" spacing={1}>
        <IconButton>
          <SocialIcon icon="google" width={22} />
        </IconButton>

        <IconButton>
          <SocialIcon icon="github" width={22} />
        </IconButton>

        <IconButton>
          <SocialIcon icon="twitter" width={22} />
        </IconButton>
      </Stack>
    </>
  );

  return (
    <>
      {renderLogo}

      {renderHead}

      <Form methods={methods} onSubmit={onSubmit}>
        {renderForm}
      </Form>

      {/* {renderSignInWithSocials} */}
    </>
  );
}
