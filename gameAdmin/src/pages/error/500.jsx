import { Button, Container, Typography } from '@mui/material';
import { ServerErrorIllustration } from 'src/assets/illustrations';
import { MotionContainer, varBounce } from 'src/components/animate';
import { m } from 'framer-motion';
import { SimpleLayout } from 'src/layouts/simple';
import { RouterLink } from 'src/routes/components';
import GdxHelmet from 'src/components/gdx/gdx-data/components/GdxHelmet';

export default function Page() {
  return (
    <>
      <GdxHelmet title="500 Internal server error!" />

      <View500 />
    </>
  );
}

function View500() {
  return (
    <SimpleLayout content={{ compact: true }}>
      <Container component={MotionContainer}>
        <m.div variants={varBounce().in}>
          <Typography variant="h3" sx={{ mb: 2 }}>
            500 Internal server error
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <Typography sx={{ color: 'text.secondary' }}>
            There was an error, please try again later.
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <ServerErrorIllustration sx={{ my: { xs: 5, sm: 10 } }} />
        </m.div>

        <Button component={RouterLink} href="/" size="large" variant="contained">
          Go to home
        </Button>
      </Container>
    </SimpleLayout>
  );
}
