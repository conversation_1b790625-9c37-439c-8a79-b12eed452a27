import { useMemo, useEffect, useCallback, useState } from 'react';

import { useSetState } from 'src/hooks/use-set-state';

import request from 'src/utils/request';

import { getLocal } from 'src/utils/storage';
import { AuthContext } from '../auth-context';

// ----------------------------------------------------------------------

export function AuthProvider({ children }) {
  const { state, setState } = useSetState({
    user: null,
    loading: true,
  });
  const [loveRole, setLoveRole] = useState(getLocal('loveRole') || 'us');

  const checkUserSession = useCallback(async () => {
    try {
      const accessToken = getLocal('accessToken');

      if (accessToken) {
        const resp = await request({
          api: '/api/admin/account/info',
        });

        setState({ user: { ...resp.model, accessToken }, loading: false });
      } else {
        setState({ user: null, loading: false });
      }
    } catch (error) {
      console.error(error);
      setState({ user: null, loading: false });
    }
  }, [setState]);

  useEffect(() => {
    checkUserSession();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ----------------------------------------------------------------------

  const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';

  const status = state.loading ? 'loading' : checkAuthenticated;

  const memoizedValue = useMemo(
    () => ({
      user: state.user
        ? {
            ...state.user,
            role: state.user?.role ?? 'admin',
          }
        : null,
      checkUserSession,
      loading: status === 'loading',
      authenticated: status === 'authenticated',
      unauthenticated: status === 'unauthenticated',
      loveRole,
      setLoveRole,
    }),
    [checkUserSession, state.user, status, loveRole, setLoveRole]
  );

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
