
import request from 'src/utils/request';
import { removeLocal, setLocal } from 'src/utils/storage';


/** **************************************
 * Sign in
 *************************************** */
export const signInWithPassword = async ({ username, password }) => {
  try {
    const params = { username, password };
    const resp = await request({
      api: '/api/admin/account/login',
      data: params,
      errorText: '登录失败！',
    })

    if (resp.status !== 0) return resp;
    const { accessToken } = resp.model;
    if (!accessToken) {
      throw new Error('Access token not found in response');
    }

    setLocal('accessToken', accessToken);
    return resp;
  } catch (error) {
    console.error('Error during sign in:', error);
    throw error;
  }
};

/** **************************************
 * Sign up
 *************************************** */
export const signUp = async ({ username, password }) => {
  const params = { username, password };

  try {
    const resp = await request({
      api: '/api/admin/account/register',
      data: params,
      errorText: '注册失败！',
    })

    if (resp.status !== 0) return resp;
    const { accessToken } = resp.model;
    if (!accessToken) {
      throw new Error('Access token not found in response');
    }

    setLocal('accessToken', accessToken);
    return resp;
  } catch (error) {
    console.error('Error during sign up:', error);
    throw error;
  }
};

/** **************************************
 * Sign out
 *************************************** */
export const signOut = async () => {
  try {
    removeLocal("accessToken")
  } catch (error) {
    console.error('Error during sign out:', error);
    throw error;
  }
};
