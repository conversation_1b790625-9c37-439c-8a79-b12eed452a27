/**
 * 存储工具类
 * 提供本地存储和会话存储的常用操作方法
 */
class StorageUtil {
    /**
     * 设置本地存储
     * @param {string} key - 键名
     * @param {any} value - 值
     */
    static setLocal(key, value) {
        try {
            const data = JSON.stringify(value);
            localStorage.setItem(key, data);
        } catch (error) {
            console.error('设置本地存储失败:', error);
        }
    }

    /**
     * 获取本地存储
     * @param {string} key - 键名
     * @returns {any} 存储的值
     */
    static getLocal(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('获取本地存储失败:', error);
            return null;
        }
    }

    /**
     * 移除本地存储
     * @param {string} key - 键名
     */
    static removeLocal(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('移除本地存储失败:', error);
        }
    }

    /**
     * 清空本地存储
     */
    static clearLocal() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('清空本地存储失败:', error);
        }
    }

    /**
     * 设置会话存储
     * @param {string} key - 键名
     * @param {any} value - 值
     */
    static setSession(key, value) {
        try {
            const data = JSON.stringify(value);
            sessionStorage.setItem(key, data);
        } catch (error) {
            console.error('设置会话存储失败:', error);
        }
    }

    /**
     * 获取会话存储
     * @param {string} key - 键名
     * @returns {any} 存储的值
     */
    static getSession(key) {
        try {
            const data = sessionStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('获取会话存储失败:', error);
            return null;
        }
    }

    /**
     * 移除会话存储
     * @param {string} key - 键名
     */
    static removeSession(key) {
        try {
            sessionStorage.removeItem(key);
        } catch (error) {
            console.error('移除会话存储失败:', error);
        }
    }

    /**
     * 清空会话存储
     */
    static clearSession() {
        try {
            sessionStorage.clear();
        } catch (error) {
            console.error('清空会话存储失败:', error);
        }
    }
}

export default StorageUtil;


export const { setLocal, getLocal, removeLocal, clearLocal, setSession, getSession, removeSession, clearSession } = StorageUtil;