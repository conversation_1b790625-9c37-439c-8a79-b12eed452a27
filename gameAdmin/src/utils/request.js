import { toast } from "sonner";

import { CONFIG } from "src/config-global";
import { getLocal, removeLocal } from "./storage";

const request = async (firstProp, ...restProps) => {
    let api, method, data, headers, errorText;
    if (typeof firstProp === 'string') {
        api = firstProp;
        data = restProps?.[0];
        method = restProps?.[1] || 'POST';
        headers = restProps?.[2] || {};
        errorText = restProps?.[3] || '请求失败！';
    } else {
        ({ api, method = 'POST', data, headers, errorText = '请求失败！' } = firstProp || {});
    }
    const accessToken = getLocal('accessToken');
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': accessToken ? `Bearer ${accessToken}` : '',
            ...headers,
        },
    };

    if (data) {
        options.body = JSON.stringify(data);
    }

    const { serverUrl } = CONFIG.site;
    const response = await fetch(serverUrl + api, options);

    if (!response.ok) {
        toast.error(errorText)
        throw new Error(response.statusText);
    }

    const resp = await response.json();
    if (resp.status !== 0) {
        toast.error(resp.message || errorText)
        if (resp.status === 401) {
            removeLocal('accessToken');
            window.location.replace('/');
        }
        return resp;
    }
    return resp;
}

export default request;