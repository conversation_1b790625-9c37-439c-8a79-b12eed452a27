import {
  Bad<PERSON>,
  Box,
  Card,
  CardA<PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>lapse,
  Divider,
  <PERSON><PERSON><PERSON><PERSON>on,
  Pagination,
  Stack,
  styled,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import GdxImage from 'src/components/gdx/gdx-data/components/GdxImage';
import { Iconify } from 'src/components/iconify';
import request from 'src/utils/request';
import { useAuthContext } from 'src/auth/hooks';
import { YxCommentForm } from '../YxCommentForm';
import { YxCommentList } from '../YxCommentList';

export default function YxLoveFinishCards() {
  const [list, setList] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  useEffect(() => {
    const fetchData = async () => {
      const resp = await request('/api/love/page', {
        query: {
          pageNo,
          pageSize: 20,
          status: 'FINISHED',
          sortOrder: -1,
          sortField: 'finishTime',
        },
      });
      if (resp.status === 0) {
        setList(resp.page.content);
        setTotalPages(resp.page.totalPages);
      }
    };
    fetchData();
  }, [pageNo]);
  return (
    <Stack spacing={1}>
      {list.map((item, index) => (
        <YxLoveFinishCard key={index} item={item} />
      ))}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
        <Pagination
          count={totalPages}
          color="primary"
          onChange={(e, page) => setPageNo(page)}
          showFirstButton
          showLastButton
        />
      </Box>
    </Stack>
  );
}

const ExpandMore = styled((props) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme }) => ({
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
  variants: [
    {
      props: ({ expand }) => !expand,
      style: {
        transform: 'rotate(0deg)',
      },
    },
    {
      props: ({ expand }) => !!expand,
      style: {
        transform: 'rotate(180deg)',
      },
    },
  ],
}));

function YxLoveFinishCard({ item }) {
  const {
    id,
    title,
    yLog,
    xLog,
    photos,
    finishTime,
    xUnReadCount = 0,
    yUnReadCount = 0,
    commentCount = 0,
  } = item;
  const [expanded, setExpanded] = useState(false);
  const [commentList, setCommentList] = useState([]);
  const { loveRole } = useAuthContext();
  const [unReadCount, setUnReadCount] = useState(0);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };
  useEffect(() => {
    if (expanded) {
      const loadComments = async () => {
        const resp = await request(`/api/love/comment/list`, { query: { loveId: id } });
        if (resp.status === 0) {
          setCommentList(resp.list.reverse());
        }
      };
      loadComments();
    }
  }, [expanded]);
  useEffect(() => {
    if (!commentList.length || !expanded) return;
    const read = async () => {
      if (loveRole === 'x' || loveRole === 'y') {
        let update = { xRead: true };
        let filterList = commentList.filter((x) => !x.xRead);

        if (loveRole === 'y') {
          update = { yRead: true };
          filterList = commentList.filter((x) => !x.yRead);
        }
        const ids = filterList.map((x) => x.id);
        if (!ids.length) return;
        await request(`/api/love/comment/batch_update`, {
          update,
          ids,
        });
        setUnReadCount(0);
      }
    };
    read();
  }, [commentList, loveRole, expanded]);
  useEffect(() => {
    if (loveRole === 'x') {
      setUnReadCount(xUnReadCount);
    } else if (loveRole === 'y') {
      setUnReadCount(yUnReadCount);
    } else {
      setUnReadCount(0);
    }
  }, [loveRole, xUnReadCount, yUnReadCount]);

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" color="pink">
          {title}
        </Typography>
        <Typography variant="body2" gutterBottom color="text.secondary">
          {dayjs(finishTime).format('YYYY-MM-DD')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          小徐：{yLog || '好想小张～让我想想说些什么'}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          小张：{xLog || '好想小徐～让我想想说些什么'}
        </Typography>
        <GdxImage value={photos} size="small" />
      </CardContent>
      <Divider sx={{ mx: 3 }} />
      <CardActions
        disableSpacing
        sx={{ justifyContent: 'space-between', alignContent: 'center', px: 3, py: 1 }}
      >
        <Typography variant="body2" color="pink">
          {expanded && '❤️ 日常碎碎念：'}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Badge badgeContent={unReadCount} color="error">
            <ExpandMore
              expand={expanded}
              onClick={handleExpandClick}
              aria-expanded={expanded}
              aria-label="show more"
            >
              <Iconify icon="eva:arrow-ios-downward-fill" />
            </ExpandMore>
          </Badge>
          <Typography variant="body2" color="text.secondary">
            {commentList.length || commentCount}
          </Typography>
        </Box>
      </CardActions>
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <CardContent sx={{ pt: 0 }}>
          <YxCommentList comments={commentList} setCommentList={setCommentList} />
          <YxCommentForm loveId={id} setCommentList={setCommentList} />
        </CardContent>
      </Collapse>
    </Card>
  );
}
