import { Card, CardContent, Typography } from '@mui/material';
import { useCountdownDate } from 'src/hooks/use-countdown';

export default function YxMeetCountdown() {
  const date = new Date('2025-08-01');
  const countdown = useCountdownDate(date);

  const renderTime = () => {
    if (!countdown) {
      return (
        <Typography variant="h6" color="error">
          已到约会时间 💖
        </Typography>
      );
    }

    const parts = [];
    if (countdown.days) parts.push(`${countdown.days}天`);
    if (countdown.hours) parts.push(`${countdown.hours}小时`);
    if (countdown.minutes) parts.push(`${countdown.minutes}分钟`);
    if (countdown.seconds !== undefined) parts.push(`${countdown.seconds}秒`);

    return (
      <Typography variant="h6" color="primary">
        {parts.join(' ')}
      </Typography>
    );
  };
  return (
    <Card sx={{ boxShadow: 4 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom color="pink">
          ❤️ 下次见面倒计时
        </Typography>
        {renderTime()}
        {/* <Box mt={2}>
          <Typography variant="body2" color="text.secondary">
            约定下次见面时间：{date.toLocaleString()}
          </Typography>
        </Box> */}
      </CardContent>
    </Card>
  );
}
