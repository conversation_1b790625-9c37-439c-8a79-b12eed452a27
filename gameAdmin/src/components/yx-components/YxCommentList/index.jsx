import { <PERSON>u, <PERSON>uI<PERSON>, Stack, Typography } from '@mui/material';
import { useState } from 'react';
import { toast } from 'sonner';
import { Iconify } from 'src/components/iconify';
import request from 'src/utils/request';

export function YxCommentList({ comments = [], setCommentList }) {
  return (
    <Stack sx={{ mb: 1 }}>
      {comments?.map((comment, index) => (
        <Item key={index} comment={comment} setCommentList={setCommentList} />
      ))}
    </Stack>
  );
}

function Item({ comment, setCommentList }) {
  const { content, name, id } = comment;
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleDelete = async () => {
    const resp = await request(`/api/love/comment/delete`, { id });
    if (resp.status === 0) {
      toast.success('删除成功！');
      setCommentList((prev) => prev.filter((x) => x.id !== id));
    }
    handleClose();
  };
  return (
    <>
      <Typography
        variant="body2"
        color="text.secondary"
        id="basic-button"
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
        sx={{ cursor: 'pointer' }}
      >
        {name}: {content}
      </Typography>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          list: {
            'aria-labelledby': 'basic-button',
          },
        }}
      >
        <MenuItem onClick={handleDelete} sx={{ color: 'red' }}>
          <Iconify icon="material-symbols:delete" sx={{ mr: 1 }} />
          删除
        </MenuItem>
      </Menu>
    </>
  );
}
