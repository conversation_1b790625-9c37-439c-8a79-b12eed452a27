// ----------------------------------------------------------------------

export const editorClasses = {
  root: 'nml__editor__root',
  toolbar: {
    hr: 'nml__editor__toolbar__hr',
    root: 'nml__editor__toolbar__root',
    bold: 'nml__editor__toolbar__bold',
    code: 'nml__editor__toolbar__code',
    undo: 'nml__editor__toolbar__undo',
    redo: 'nml__editor__toolbar__redo',
    link: 'nml__editor__toolbar__link',
    clear: 'nml__editor__toolbar__clear',
    image: 'nml__editor__toolbar__image',
    italic: 'nml__editor__toolbar__italic',
    strike: 'nml__editor__toolbar__strike',
    hardbreak: 'nml__editor__toolbar__hardbreak',
    unsetlink: 'nml__editor__toolbar__unsetlink',
    codeBlock: 'nml__editor__toolbar__code__block',
    alignLeft: 'nml__editor__toolbar__align__left',
    fullscreen: 'nml__editor__toolbar__fullscreen',
    blockquote: 'nml__editor__toolbar__blockquote',
    bulletList: 'nml__editor__toolbar__bullet__list',
    alignRight: 'nml__editor__toolbar__align__right',
    orderedList: 'nml__editor__toolbar__ordered__list',
    alignCenter: 'nml__editor__toolbar__align__center',
    alignJustify: 'nml__editor__toolbar__align__justify',
  },
  content: {
    hr: 'nml__editor__content__hr',
    root: 'nml__editor__content__root',
    link: 'nml__editor__content__link',
    image: 'nml__editor__content__image',
    codeInline: 'nml__editor__content__code',
    heading: 'nml__editor__content__heading',
    listItem: 'nml__editor__content__listItem',
    codeBlock: 'nml__editor__content__code__block',
    blockquote: 'nml__editor__content__blockquote',
    langSelect: 'nml__editor__content__lang__select',
    placeholder: 'nml__editor__content__placeholder',
    bulletList: 'nml__editor__content__bullet__list',
    orderedList: 'nml__editor__content__ordered__list',
  },
};
