
import { Loading<PERSON>utton } from "@mui/lab";
import { <PERSON>, Grid, Alert, Dialog, Button, DialogTitle, DialogContent } from "@mui/material";

import { getDialogZIndex } from "../gdx-data/const";
import GdxForm, { useGdxFormContext } from "../gdx-form";
import { useLangTranslate } from "../gdx-data/context/common";



export default function GdxFormDialog({ embed = false, useGrid = false, model = {}, width, column = 2, helperText, title, open, onClose, children, ...restProps }) {
    const dialogContent = <><DialogTitle sx={{ boxShadow: !embed ? "0px 2px 5px #dfe3e8" : "none" }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: "space-between" }}>
            <div>{title}</div>
            <GdxDialogActions onClose={onClose} />
        </div>
    </DialogTitle>
        <DialogContent sx={{ flex: 1, flexGrow: 1, paddingBottom: 3 }}>
            {helperText && <Alert variant="outlined" severity="info" sx={{ mb: 3 }}>
                {helperText}
            </Alert> || <Box sx={{ height: '1rem' }} />}

            {useGrid && <Grid container rowSpacing={3} columnSpacing={{ xs: 1, sm: column }}>
                {children}
            </Grid> || <Box
                rowGap={3}
                columnGap={2}
                display="grid"
                gridTemplateColumns={{
                    xs: 'repeat(1, 1fr)',
                    sm: `repeat(${column}, 1fr)`,
                }}
            >
                    {children}
                </Box>}
        </DialogContent></>
    return (
        <GdxForm defaultValues={model} {...restProps}>
            {embed ? <Box sx={{ width: width || column === 2 && 720 || column === 1 && 360 || 720, maxWidth: 1 }}>{dialogContent}</Box> : <Dialog
                fullWidth
                keepMounted
                maxWidth={false}
                open={open && true || false}
                onClose={(e, reason) => {
                    console.log(reason);
                }}
                PaperProps={{
                    sx: { width: width || column === 2 && 720 || column === 1 && 360 || 720, maxWidth: 1 },
                }}
                sx={{ zIndex: getDialogZIndex() }}
            >
                {dialogContent}
            </Dialog>}
        </GdxForm>
    )
}

function GdxDialogActions({ onClose }) {
    const { rl } = useLangTranslate();
    const { onSubmit, loading } = useGdxFormContext();
    return (<div>
        <Button variant="outlined" onClick={onClose} sx={{ marginRight: 1 }}>
            {rl('Cancel')}
        </Button>
        <LoadingButton variant="contained" loading={loading} onClick={onSubmit}>
            {rl('Save')}
        </LoadingButton>
    </div>)
}