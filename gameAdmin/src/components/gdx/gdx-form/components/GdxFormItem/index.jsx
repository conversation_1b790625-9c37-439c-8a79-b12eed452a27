import { Controller, useFormContext } from "react-hook-form";
import { useMemo, useState, useEffect, useCallback, cloneElement } from "react";

import { useLangTranslate } from "src/components/gdx/gdx-data/context/common";

import { Yupx, useGdxFormContext } from "../..";



export default function GdxFormItem({ condition, ...restProps }) {
    if (!condition) {
        return <GdxFormItemInside {...restProps}></GdxFormItemInside>
    }
    return <GdxFormItemCondition condition={condition} {...restProps}></GdxFormItemCondition>
}

function GdxFormItemCondition({ condition, ...restProps }) {
    const [cc, setCc] = useState();
    const { watch } = useFormContext();
    const data = watch();

    useEffect(() => {
        const ret = condition(data);
        if (ret) {
            if (typeof (ret) === 'object') {
                setCc(ret);
            } else {
                setCc({});
            }
        } else {
            setCc(null);
        }
    }, [data, condition])
    if (!cc) return null;
    return (
        <GdxFormItemInside {...restProps} {...cc}></GdxFormItemInside>
    )
}
function GdxFormItemInside({ name, children, ...restProps }) {
    const { label, required, validator } = restProps;
    const { rl } = useLangTranslate();
    const { control } = useFormContext();
    const { addFormValidator, removeFormValidator } = useGdxFormContext();
    const [va, setVa] = useState();
    useEffect(() => {
        if (validator) {
            setVa(validator);
        } else if (required) {
            setVa(Yupx.string().required(rl(`${label || rl('Field')} is required`)));
        }
    }, [required, validator])

    useEffect(() => {
        if (!va) return;
        addFormValidator(name, va);
        return () => {
            removeFormValidator(name);
        }
    }, [va])

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState: { error } }) => <FormItemInside field={field} error={error} children={children} {...restProps}></FormItemInside>} />
    )
}

function FormItemInside({ field, error, children, defaultValue, required, label, width, ...restProps }) {
    const [value, setValue] = useState();
    useEffect(() => {
        if (!field || field.value === undefined) {
            if (defaultValue) {
                onChange(defaultValue);
                setValue(defaultValue);
            }
            return;
        }
        setValue(field.value);
    }, [field])
    const onChange = useCallback((v) => {
        if (v && v.target && v._reactName) {
            v = v.target.value;
        }
        if (restProps.onChange) restProps.onChange(v);
        field.onChange(v);
    }, [field])
    const ele = useMemo(() => {
        const props = {
            onChange,
            error: !!error,
            sx: {
                width: 1
            },
            value
        }
        if (width) {
            props.md = {
                width
            }
        }
        if (required && label) {
            props.label = <span>{label} <b style={{ color: 'red' }}>*</b></span>
        } else if (label) {
            props.label = label;
        }
        if (error) {
            props.helperText = error?.message || (restProps).helperText;
        }
        return cloneElement(children, props);
    }, [value, onChange, error, required, label, restProps]);
    return ele;
}