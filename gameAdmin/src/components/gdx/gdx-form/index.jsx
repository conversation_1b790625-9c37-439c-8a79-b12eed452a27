// import { objectDel, objectSet } from "src/utils/utils";
import * as Yup from 'yup'
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm, FormProvider, useFormContext } from "react-hook-form";
import { useRef, useMemo, useState, useEffect, useContext, useCallback, cloneElement, createContext } from "react";


const GdxFormContext = createContext({ loading: false });

export const Yupx = Yup;

export default function GdxForm({ children, onSubmit, defaultValues, validators, ...restProps }) {
    const [schema, setSchema] = useState({});
    const formValidatorsRef = useRef({})
    const [formValidators, setFormValidators] = useState({});
    const [loading, setLoading] = useState(false);
    const methods = useForm({
        resolver: yupResolver(Yup.object().shape(schema)),
        defaultValues,
    });
    const { handleSubmit } = methods;
    const onSubmitInside = onSubmit ? handleSubmit(onSubmit) : undefined;
    const addFormValidator = useCallback((key, validator) => {
        // if (!validator) {
        //     delete formValidatorsRef.current[key]
        // } else {
        //     formValidatorsRef.current[key] = validator;
        // }
        // setFormValidators({...formValidatorsRef.current});
    }, [formValidators, formValidatorsRef])
    const removeFormValidator = useCallback((key) => {
        delete formValidatorsRef.current[key];
        setFormValidators({ ...formValidatorsRef.current });
    }, [formValidators, formValidatorsRef])
    const memV = useMemo(() => (
        {
            onSubmit: async () => {
                if (!onSubmitInside) return;
                setLoading(true);
                try {
                    await onSubmitInside();
                } finally {
                    setLoading(false);
                }
            },
            loading, addFormValidator, removeFormValidator
        }
    ), [onSubmitInside, loading, addFormValidator, removeFormValidator])

    useEffect(() => {
        setSchema({ ...validators, ...formValidators });
    }, [validators, formValidators])
    return (
        <FormProvider {...methods}>
            <GdxFormContext.Provider value={memV}>
                <form onSubmit={async (e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    await onSubmitInside(e);
                }} {...restProps}>{children}</form>
            </GdxFormContext.Provider>
        </FormProvider>
    )
}


export function GdxWatch({ children, onWatch }) {
    const { watch } = useFormContext();
    const watchAllFields = watch();
    useEffect(() => {
        onWatch(watchAllFields);
    }, [watchAllFields])
    return children || null;
}


export function useGdxFormContext() {
    return useContext(GdxFormContext);
}
export function GdxFormButtonWrapper({ children }) {
    const [loading, setLoading] = useState(false);
    const { onSubmit } = useContext(GdxFormContext);
    if (!children) return null;
    return cloneElement(children, {
        loading, onClick: async () => {
            if (!onSubmit) return;
            setLoading(true);
            await onSubmit();
            setLoading(false);
        }
    });
}