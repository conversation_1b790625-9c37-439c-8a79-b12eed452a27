import { CONFIG } from "src/config-global";

export const GdxItemMdWidth = 200;


export function getResourceImageUrl(resId) {
    const { serverUrl } = CONFIG.site;
    return `${serverUrl}/api/resources/image/${resId}`;
}

export function getResourcePath(resId) {
    const { serverUrl } = CONFIG.site;
    return `${serverUrl}/api/file/download/${resId}`;
}

export function getResourceName(resId) {
    const i = resId.indexOf('__S');
    const j = resId.indexOf('F__', i);
    if (i > 0 && j > i) {
        let ret = decodeURIComponent(resId.substring(i + 3, j));
        ret = ret.replace(/_DOT_/, '.');
        return ret;
    }
    return resId;
}

let zIndex = 2;

export function getDialogZIndex() {
    const ret = zIndex;
    zIndex += 1;
    return ret;
}