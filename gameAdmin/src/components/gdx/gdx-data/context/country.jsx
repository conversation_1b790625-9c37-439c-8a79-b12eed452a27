import { useMemo, useState, useEffect, useContext, useCallback, createContext } from "react";

import request from "src/utils/request";

import { useGdxTypeData } from "./hook";

const CountryContext = createContext({});

export function CountryContextProvider({ children }) {
    const [timezone, setTimezone] = useState();
    const { country, countryId, setCountryId, countryList } = useCountry();
    useEffect(() => {
        if (!countryId) return;
        const loadTimezone = async () => {
            const resp = await request('/api/auth/timezone', {});
            if (resp.status === 0) {
                setTimezone(resp.model);
            }
        }
        loadTimezone();
    }, [countryId])
    const mv = useMemo(() => ({ timezone, country, countryId, setCountryId, countryList }),
        [country, countryId, setCountryId, countryList, timezone]);
    if (!country || !timezone) {
        return null;
    }
    return (
        <CountryContext.Provider value={mv}>
            {children}
        </CountryContext.Provider>
    )
}

export function useCountryContext() {
    return useContext(CountryContext);
}

function useCountry() {
    const { typeData } = useGdxTypeData({ type: 'enable-country' });
    const [countryList, setCountryList] = useState();
    const [country, setCountryOrig] = useState();
    useEffect(() => {
        if (!typeData || !typeData.options) {
            return;
        }
        setCountryList(typeData.options);
    }, [typeData])
    useEffect(() => {
        if (country) return;
        if (!countryList || !countryList.length) {
            return;
        }
        const c = localStorage.getItem('countryId');
        const clist = countryList;
        let cc;
        if (c) {
            cc = clist.find((x) => x.id === c);
        }
        if (!cc) {
            cc = clist[0];
            localStorage.setItem('countryId', cc.id);
        }
        setCountryOrig(cc);
    }, [countryList, country])
    const setCountryId = useCallback((cid) => {
        const c = countryList.find((x) => x.id === cid);
        if (c) {
            localStorage.setItem('countryId', c.id);
            setCountryOrig(c);
        }
    }, [setCountryOrig, countryList]);
    return { country, setCountryId, countryId: country && country.id, countryList: countryList || [] };
}