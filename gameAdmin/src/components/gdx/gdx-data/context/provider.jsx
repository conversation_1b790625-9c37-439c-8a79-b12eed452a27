import React, { useMemo, useState, useEffect, useReducer, useCallback } from "react";

import { Box } from "@mui/material";

import request from "src/utils/request";
// import { onRequestAfter, onRequestError } from "src/utils/axios";

import { useTranslate } from "src/locales";

import { ConfirmDialog } from "src/components/custom-dialog";

// import { initPermissions } from "src/auth/context/jwt/utils";

import { initData } from "./init";
import { GdxDataContext } from "./context";
import GdxInput from "../components/GdxInput";
import GdxButton from "../components/GdxButton";
import { syncRunning, useLangTranslate } from "./common";



const dataMap = {};

let confirmId = 1;

const reducer = (state, action) => {
    const { type, payload } = action;
    if (type === 'showConfirm') {
        return [...state, payload];
    }
    if (type === 'hideConfirm') {
        return state.filter((x) => (x.id !== payload.id));
    }
    return state;
};

const confirmIds = {};

// export function InitProvider({ children }) {
//     const [inited, setInited] = useState(false);
//     useEffect(() => {
//         const init = async () => {
//             await initPermissions();
//             setInited(true);
//         }
//         init();
//     }, [])
//     if (!inited) return null;
//     return children;
// }

export function GdxDataContextProvider({ children }) {
    const [confirms, dispatchConfirms] = useReducer(reducer, []);
    const { t } = useTranslate();
    const { rl } = useLangTranslate();
    const register = useCallback((type, data) => {
        dataMap[type] = data;
    }, []);
    const [inited, setInited] = useState(false);
    const getData = useCallback(async (type) => {
        const data = dataMap[type];
        if (!data) return null;
        await syncRunning(type, async () => {
            if (!data.options || data.dirty) {
                if (data.type === 'url') {
                    const resp = await request(data.url, data.query);
                    if (resp.status === 0) {
                        data.options = resp.list;
                    } else {
                        data.options = [];
                    }
                    data.dirty = false;
                } else if (data.type === 'type-define') {
                    const resp = await request('/api/type_define/list', { query: { categoryId: data.tdcId } });
                    if (resp.status === 0) {
                        resp.list.forEach((x) => {
                            x.id = x.value;
                        })
                        data.options = resp.list;
                    } else {
                        data.options = [];
                    }
                    data.dirty = false;
                } else if (data.type === 'redirect') {
                    const nextData = await getData(data.sourceType);
                    let opts = nextData.options;
                    opts = opts.map((x) => data.map(x)).filter((x) => x);
                    data.options = opts;
                }
            }
        });
        return data;
    }, [])
    useEffect(() => {
        // onRequestAfter(({ path }) => {
        //     Object.entries(dataMap).forEach(item => {
        //         const v = item[1];
        //         if (v.watch && v.watch.indexOf(path) >= 0) {
        //             v.dirty = true;
        //         }
        //     })
        // })
    }, [])
    const showConfirm = useCallback((data) => {
        confirmId += 1;
        dispatchConfirms({ type: 'showConfirm', payload: { ...data, id: confirmId } });
        confirmIds[confirmId] = true;
        return confirmId;
    }, [])
    const isShowConfirm = useCallback((id) => confirmIds[id] && true || false, [])
    useEffect(() => {
        initData({ register, rl })
        setInited(true);
    }, [t, register, rl])
    const memoizedValue = useMemo(
        () => ({
            getData,
            showConfirm,
            isShowConfirm
        }),
        [getData, showConfirm, isShowConfirm]
    );
    if (!inited) return null;
    return (
        <GdxDataContext.Provider value={memoizedValue}>
            {children}
            {confirms && confirms.map((confirm) => (
                <ConfirmDialog
                    key={confirm.id}
                    open={confirm && true || false}
                    onClose={() => {
                        delete confirmIds[confirm.id];
                        dispatchConfirms({ type: 'hideConfirm', payload: confirm })
                    }}
                    title={confirm?.title}
                    content={confirm?.content}
                    action={
                        <GdxButton variant="contained" color={confirm?.okColor || 'inherit'} onClick={async () => {
                            if (confirm?.onOk) {
                                const ret = await confirm.onOk();
                                if (ret === 'error') {
                                    return;
                                }
                            }
                            delete confirmIds[confirm.id];
                            dispatchConfirms({ type: 'hideConfirm', payload: confirm })
                        }}>
                            {confirm?.okText || rl('Ok')}
                        </GdxButton>
                    }
                />
            ))}
        </GdxDataContext.Provider>
    )
}

function PasswordInput({ onChange }) {
    const { rl } = useLangTranslate();
    const [value, setValue] = useState();
    return <Box sx={{ padding: 3, width: 1 }}>
        <GdxInput sx={{ width: 1 }} label={rl('Input')} value={value} onChange={(v) => {
            setValue(v);
            onChange(v);
        }}></GdxInput>
    </Box>
}

// export function RequestErrorProvider({ children }) {
//     const { showConfirm, isShowConfirm } = useGdxDataContext();
//     const { rl } = useLangTranslate();

//     useEffect(() => {
//         onRequestError(async ({ resp, redo }) => {
//             if (resp.status === 110110) {
//                 const po = JSON.parse(resp.message);
//                 let pwd;
//                 let finalR;
//                 const cid = showConfirm({
//                     title: po.title,
//                     content: po.password && <PasswordInput onChange={(v) => { pwd = v }}></PasswordInput>,
//                     okText: rl('Ok'),
//                     okColor: 'warning',
//                     onOk: async () => {
//                         if (po.password && !pwd) {
//                             // enqueueSnackbar(rl("${p} is required", rl('Input')), { variant: 'error' })
//                             toast.error(rl("Input is required"))
//                             return "error";
//                         }
//                         const r = await request('/api/lt_force_auth/auth', { authKey: po.authKey, password: pwd });
//                         if (r.status === 0) {
//                             finalR = await redo();
//                         } else {
//                             return "error";
//                         }
//                     }
//                 })
//                 while (true) {
//                     await sleep(100);
//                     if (!isShowConfirm(cid)) {
//                         break;
//                     }
//                 }

//                 return finalR;
//             }
//             // enqueueSnackbar(resp.message, {
//             //     variant: 'error',
//             // })
//             toast.error(resp.message)
//         })
//     }, [rl, showConfirm])
//     return <>
//         {children}
//     </>
// }