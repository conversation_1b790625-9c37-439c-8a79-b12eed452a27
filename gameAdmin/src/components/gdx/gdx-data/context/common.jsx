import md5 from "md5";
import { useMemo, useState, useEffect, useContext, useCallback, createContext } from "react";

import request from "src/utils/request";
import { localStorageGetItem } from "src/utils/storage-available";

import { useTranslate } from "src/locales";


const runMap = {};
function sleep(time) {
    return new Promise(resolve => {
        setTimeout(resolve, time);
    })
}
export function syncRunning(key, runnable) {
    return new Promise((resolve) => {
        const run = async () => {
            try {
                while (runMap[key]) {
                    await sleep(100);
                }
                runMap[key] = true;
                await runnable();
                resolve();
            } finally {
                delete runMap[key];
            }
        }
        run();
    })
}


const langCache = {};
function runRl(key, p, langData) {
    langData = langData || {};
    if (!key) return "";
    let v = langData[md5(key).toUpperCase()];
    if (!v) {
        v = key;
    }
    if (p !== undefined) {
        v = eval("`" + v + "`"); // eslint-disable-line prefer-template,no-eval
    }
    return v;
}

const LangContext = createContext({ ok: false });

export function LangProvider({ children }) {
    const { ok } = useLangOk();
    const memV = useMemo(() => ({ ok }), [ok]);
    return <LangContext.Provider value={memV}>
        {children}
    </LangContext.Provider>
}

function useLangOk() {
    const { t } = useTranslate();
    const currentLang = useMemo(() => (localStorageGetItem('i18nextLng')), [t]);
    const [ok, setOk] = useState(false);

    useEffect(() => {
        if (!currentLang) return;
        const loadLang = async () => {
            const langMap = { "en": 'en-us', "cn": 'zh-cn' };
            const resp = await request('/api/bix/gb_lang_item/map', { lang: langMap[currentLang] });
            if (resp.status === 0) {
                langCache[currentLang] = resp.model;
                setOk(new Date().getTime());
            }
        }
        const syncLoading = () => {
            syncRunning(currentLang, async () => {
                if (langCache[currentLang]) {
                    setOk(new Date().getTime());
                    return;
                }
                await loadLang();
            })
        }
        syncLoading();
    }, [currentLang])
    return { ok }
}

export function useLangTranslate() {
    const { ok } = useContext(LangContext);
    const rl = useCallback((key, p) => {
        const isZhCn = navigator.language.toLowerCase() === 'zh-cn';
        const userLanguage = isZhCn ? 'cn' : 'en';
        const lang = localStorageGetItem('i18nextLng') || userLanguage;
        const langData = langCache[lang];
        const ret = runRl(key, p, langData);
        return ret;
    }, [ok]);
    return {
        rl
    }
}


