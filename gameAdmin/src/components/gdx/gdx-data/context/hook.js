import { useState, useEffect, useContext } from "react";

import { GdxDataContext } from "./context";

export function useGdxDataContext() {
    const context = useContext(GdxDataContext);
    return context;
}



export function useGdxTypeData({ type }) {
    const { getData } = useGdxDataContext();
    const [typeData, setTypeData] = useState();
    useEffect(() => {
        const init = async () => {
            if (!type) return;
            const data = await getData(type);
            if (data) {
                setTypeData(data);
            }
        }
        if (type) {
            init();
        }
    }, [getData, type])
    return { typeData };
}