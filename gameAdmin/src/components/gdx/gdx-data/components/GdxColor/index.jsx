import { useState } from "react";
import { SketchPicker } from "react-color";

import { Button } from "@mui/material";

import GdxInput from "src/components/gdx/gdx-data/components/GdxInput";

export default function GdxColor({ label, value, onChange, ...restProps }) {
    const [show, setShow] = useState(false);
    return (
        <div style={{ display: 'flex', alignItems: 'center', position: "relative" }}>
            <GdxInput label={label} value={value} onChange={onChange} {...restProps} style={{ flex: 1 }} />
            <Button style={{ width: 40, border: 'solid 1px black', height: 40, cursor: 'pointer', marginLeft: 2, display: 'inline-flex', background: value }} onClick={() => { setShow(!show) }} />
            {show && <div style={{ position: "absolute", zIndex: 66 }}>
                <SketchPicker color={value || 'black'} onChange={(v) => {
                    if (onChange) {
                        onChange(v.hex);
                    }
                }} />
            </div>}
        </div>
    )
}