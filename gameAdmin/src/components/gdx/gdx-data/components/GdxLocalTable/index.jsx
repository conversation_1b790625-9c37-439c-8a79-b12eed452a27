import { useState, useEffect } from "react";

import uuidv4 from "src/utils/uuidv4";

import RemoteTable from "src/components/gdx/remote-table"

import GdxOutlineBox from "../GdxOutlineBox"



export default function GdxLocalTable({ label, rowKey = "id", editFormColumn = 1, editFormWidth = 500, size = "small", value, onChange, ...restProps }) {
    const [vs, setVs] = useState([]);
    const [k, setK] = useState("_");
    console.log("rowKey", rowKey)
    useEffect(() => {
        const vvv = value || [];
        vvv.forEach(x => {
            if (!x.id) {
                x.id = uuidv4();
            }
        })
        setVs(vvv)
        setK(`${new Date().getTime()}_`);
    }, [value])
    return (
        <GdxOutlineBox label={label} size={size}>
            <RemoteTable key={k} options={{
                size,
                // batchActions: ['edit', 'delete'],
                ...restProps,
                editFormColumn,
                editFormWidth,
                baseUrl: "local",
                hidePage: true,
                requestHook: {
                    apiPost: async (path, data) => {
                        if (path === 'local/page') {
                            const v = vs;
                            return {
                                "status": 0,
                                "page": {
                                    "number": 1,
                                    "size": v.length,
                                    "numberOfElements": v.length,
                                    "totalPages": 1,
                                    "content": v,
                                    "totalElements": v.length
                                }
                            }
                        }
                        if (path === 'local/up') {
                            const tm = vs[data._rowIndex];
                            const preIndex = (vs.length + data._rowIndex - 1) % vs.length;
                            vs[data._rowIndex] = vs[preIndex]
                            vs[preIndex] = tm;
                            if (onChange) {
                                onChange([...vs]);
                            }
                            return {
                                "status": 0
                            }
                        }
                        if (path === 'local/persist') {
                            const { model } = data;
                            if (data._rowIndex >= 0) {
                                vs[data._rowIndex] = model;
                            } else {
                                vs.push(model);
                            }
                            if (onChange) {
                                onChange([...vs]);
                            }
                            return {
                                status: 0
                            }
                        }
                        if (path === 'local/delete') {
                            const nvs = vs.filter((x, index) => (index !== data._rowIndex));
                            if (onChange) {
                                onChange([...nvs])
                            }
                            return {
                                status: 0
                            }
                        }
                    }
                }
            }} />
        </GdxOutlineBox>
    )
}