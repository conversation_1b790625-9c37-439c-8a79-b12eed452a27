import { useState, useEffect } from 'react';

import { Grid } from '@mui/material';

import GdxColor from '../GdxColor';
import GdxInput from '../GdxInput';
import { useLangTranslate } from '../../context/common';


export default function GdxStylesEditor({ value, onChange }) {
	const { rl } = useLangTranslate();
	const [styles, setStyles] = useState({});
	const typeList = [
		{ label: rl("Background Color"), key: "background", type: "color", span: 12 },
		{ label: rl("Width"), key: "width", type: "text", span: 6 },
		{ label: rl("Max Width"), key: "maxWidth", type: "text", span: 6 },
		{ label: rl("Height"), key: "height", type: "text", span: 6 },
		{ label: rl("Max Height"), key: "maxHeight", type: "text", span: 6 },
		{ label: rl("Margin"), key: "margin", type: "text", span: 6 },
		{ label: rl("Padding"), key: "padding", type: "text", span: 6 },
		{ label: rl("Border"), key: "border", type: "text", span: 12 },
		{ label: rl("Border Radius"), key: "borderRadius", type: "text", span: 12 },
	]
	const onStyleChange = (key, v) => {
		const obj = styles;
		obj[key] = v;
		setStyles({ ...obj })
		onChange({ ...obj });
	}
	useEffect(() => {
		setStyles(value || {});
	}, [value])
	return (
		<Grid container spacing={2}>
			{
				typeList.map((item, index) => (
					<Grid item xs={item.span} key={index}>
						{item.type === "text" && <GdxInput label={item.label} value={styles[item.key]} onChange={(v) => onStyleChange(item.key, v)} sx={{ width: 1 }} />}
						{item.type === "color" && <GdxColor label={item.label} value={styles[item.key]} onChange={(v) => onStyleChange(item.key, v)} />}
					</Grid>
				))
			}
		</Grid>
	)
}