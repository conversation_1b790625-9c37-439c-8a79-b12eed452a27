.image {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid #d9d9d9;
    position: relative;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background: #00000052;
        cursor: zoom-in !important;
    }

    &:global(.small) {
        width: 80px;
        height: 80px;
    }

    &:global(.mini) {
        width: 32px;
        height: 32px;
    }

    .delete {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
        align-items: center;
        justify-content: center;
        z-index: 1;
        background-color: rgba(0, 0, 0, 0.353);
        width: 18px;
        height: 18px;
    }
}