import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useState, useEffect } from 'react';

import { DatePicker } from '@mui/x-date-pickers';

import { useCountryContext } from '../../context/country';

dayjs.extend(utc);
dayjs.extend(timezone);

export default function GdxMonthPicker({ label, value, onChange, ...restProps }) {
  const { timezone: tz } = useCountryContext();
  const [v, setV] = useState(null);
  useEffect(() => {
    if (value) {
      setV(dayjs.tz(value, tz).startOf('month'));
    } else {
      setV(null);
    }
  }, [value, tz]);
  return (
    <DatePicker
      label={label}
      value={v}
      views={['month', 'year']}
      onChange={(nv) => {
        if (onChange) {
          onChange(nv ? nv.startOf('month').valueOf() : null);
        }
      }}
      timezone={tz}
      slotProps={{
        field: { clearable: true },
      }}
      sx={{
        maxWidth: { xs: 1, md: 200 },
      }}
      {...restProps}
    />
  );
}
