import { Switch, FormControlLabel } from "@mui/material";


export default function GdxSwitch({ label, value, onChange, style, ...restProps }) {
    return (
        <FormControlLabel control={<Switch checked={value && true || false} onChange={(e, checked) => {
            if (onChange) {
                onChange(checked);
            }
        }} />} label={label} {...restProps} style={{ ...style, width: 'fit-content' }} />
    )
}