import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import tzPlugin from 'dayjs/plugin/timezone';
import numeral from 'numeral';
import { useState, useEffect } from 'react';

import { Box, ListItemText } from '@mui/material';

import { fNumber } from 'src/utils/format-number';

import GdxTag from '../GdxTag';
import GdxImage from '../GdxImage';
import GdxIdView from '../GdxIdView';
import GdxIpView from '../GdxIpView';
import GdxLinkButton from '../GdxLinkButton';
import { useGdxTypeData } from '../../context/hook';
import { useLangTranslate } from '../../context/common';
import { useCountryContext } from '../../context/country';

dayjs.extend(utc);
dayjs.extend(tzPlugin);

function compareId(id1, id2) {
  if (!id1 && !id2) return true;
  return `${id1}` === `${id2}`;
}

export default function GdxValue({
  value,
  dataSource,
  type,
  timezone: propTimezone,
  row,
  rowIndex,
  emptyShowAll = false,
  ...restProps
}) {
  const { timezone: defaultTimezone } = useCountryContext();
  const { rl } = useLangTranslate();
  const timezone = propTimezone || defaultTimezone;
  if (emptyShowAll) {
    if (!value || !value.length) {
      return <GdxTag>{rl('ALL')}</GdxTag>;
    }
  }
  if (value !== undefined) {
    if (!type) {
      return value;
    }
    if (dataSource && dataSource[type]) {
      const data = dataSource[type];
      const rr = data.find((x) => x.id === value);
      if (rr) return rr.name;
      return value;
    }
    if (type === 'multiple') {
      return value.map((x, i) => `${i > 0 ? ' , ' : ''}${x}`);
    }
    if (type === 'index') {
      return (
        <div style={{ width: 80, fontWeight: 800, fontSize: '1.2rem' }}>{(rowIndex || 0) + 1}</div>
      );
    }
    if (type === 'percent') {
      return `${((value || 0) * 100).toFixed(2)}%`;
    }
    if (type === 'id') {
      return <GdxIdView value={value}></GdxIdView>;
    }
    if (type === 'amount') {
      return fNumber(value || 0);
    }
    if (type === 'date') {
      if (!value) return null;
      return (
        <ListItemText
          primary={dayjs.tz(new Date(value), timezone).format('DD MMM YYYY')}
          primaryTypographyProps={{ typography: 'body2', noWrap: true }}
        />
      );
    }
    if (type === 'month') {
      if (!value) return null;
      return (
        <ListItemText
          primary={dayjs.tz(new Date(value), timezone).format('MMM YYYY')}
          primaryTypographyProps={{ typography: 'body2', noWrap: true }}
        />
      );
    }
    if (type === 'datetime') {
      if (!value) return null;
      return (
        <ListItemText
          primary={dayjs.tz(new Date(value), timezone).format('DD MMM YYYY')}
          secondary={dayjs.tz(new Date(value), timezone).format('HH:mm:ss')}
          primaryTypographyProps={{ typography: 'body2', noWrap: true }}
          secondaryTypographyProps={{
            mt: 0.5,
            component: 'span',
            typography: 'caption',
          }}
        />
      );
    }
    if (type === 'image' || type === 'icon') {
      return <GdxImage size="small" value={value}></GdxImage>;
    }
    if (type === 'number') {
      return numeral(value || 0).format('0,0');
    }
    if (type === 'domain' || type === 'url' || type === 'link') {
      return <GdxLinkButton value={value}></GdxLinkButton>;
    }
    if (type === 'ip') {
      return <GdxIpView value={value} row={row}></GdxIpView>;
    }
    if (Array.isArray(value)) {
      return value.map((v, index) => (
        <Box key={index} sx={{ padding: '0.125rem', display: 'inline-flex' }}>
          <GdxSingleValue type={type} key={index} value={v} {...restProps}></GdxSingleValue>
        </Box>
      ));
    }
  }

  return <GdxSingleValue type={type} value={value} {...restProps}></GdxSingleValue>;
}

function GdxSingleValue({ value, type }) {
  const { typeData } = useGdxTypeData({ type });
  const [item, setItem] = useState();
  useEffect(() => {
    if (!typeData || value === undefined || !typeData.options) return;
    const ii = typeData.options.find((x) => compareId(x.id, value));
    if (ii) {
      setItem(ii);
    }
  }, [typeData, value]);
  if (item) {
    return <GdxTag color={item.color}>{item.name}</GdxTag>;
  }
  return null;
}
