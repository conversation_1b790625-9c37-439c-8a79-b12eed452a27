
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import { alpha, useTheme } from '@mui/material/styles';

import { bgGradient } from 'src/theme/css';



export default function GdxItemsDataDisplay({
    items = [],
    color = 'primary',
    sx,
    ...other
}) {
    const theme = useTheme();

    return (
        <Stack
            sx={{
                ...bgGradient({
                    direction: '135deg',
                    startColor: alpha(theme.palette[color].light, 0.2),
                    endColor: alpha(theme.palette[color].main, 0.2),
                }),
                p: 3,
                borderRadius: 2,
                color: `${color}.darker`,
                backgroundColor: 'common.white',
                ...sx,
            }}
            {...other}
        >
            <Stack direction="row" justifyContent="space-around">

                {items.map((item, index) => (
                    <div style={{ textAlign: 'center' }} key={index}>
                        <Box sx={{ mb: 1, typography: 'subtitle2' }}>{item.label}</Box>
                        <Box sx={{ typography: 'h3' }}>{item.value}</Box>
                    </div>
                ))}
            </Stack>
        </Stack>
    );
}
