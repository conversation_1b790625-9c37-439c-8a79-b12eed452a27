import { useState, useEffect, useCallback } from "react";

import { ClearIcon } from "@mui/x-date-pickers";
import { TextField, IconButton } from "@mui/material";

import { GdxItemMdWidth } from "../../const";


export default function GdxInput({ value, onChange, sx, size = 'medium', showClear = false, ...restProps }) {
    const [v, setV] = useState('');
    useEffect(() => {
        setV(value || '');
    }, [value])
    const change = useCallback(() => {
        if ((v || '') === (value || '')) return;
        if (onChange) onChange(v);
    }, [onChange, v, value])

    const clear = (() => {
        setV('');
        if (onChange) onChange('');
    })

    return (
        <TextField
            value={v}
            onChange={(e) => { setV(e.target.value) }}
            onBlur={change}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.keyCode === 13) {
                    change();
                }
            }}
            sx={{ width: { xs: 1, md: GdxItemMdWidth }, minWidth: 200, ...sx }}
            size={size}
            {...restProps}
            InputProps={{
                endAdornment: showClear && <IconButton onClick={clear}><ClearIcon /></IconButton>,
            }}
        />
    )
}