import { TextField, InputAdornment } from "@mui/material";

import { GdxItemMdWidth } from "../../const";



export default function GdxInputNumber({ value, endAdornmentChildren, ...restProps }) {
    return (
        <TextField
            type="number"
            value={value}
            InputProps={{
                endAdornment: endAdornmentChildren && <InputAdornment position="end">{endAdornmentChildren}</InputAdornment>,
            }}
            sx={{ width: { xs: 1, md: GdxItemMdWidth } }}
            {...restProps}
        />
    )
}