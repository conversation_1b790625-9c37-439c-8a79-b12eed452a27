import MDEditor from '@uiw/react-md-editor';
import { useRef, useState, useEffect } from 'react';

import GdxOutlineBox from '../GdxOutlineBox';


export function GdxMdEditor({ label, value, onChange }) {
    const [v, setV] = useState(value);
    const ref = useRef(new Date().getTime())

    useEffect(() => {
        setV(value);
    }, [value])

    return (
        <GdxOutlineBox label={label}>
            <MDEditor value={v} onChange={(nv) => {
                setV(nv);
                ref.current = new Date().getTime();
                const t = ref.current;
                setTimeout(() => {
                    if (t !== ref.current) return;
                    if (onChange) {
                        ref.current = new Date().getTime();
                        onChange(nv);
                    }
                }, 2000)
            }} onBlur={() => {
                if (onChange) onChange(v);
            }}></MDEditor>
        </GdxOutlineBox>
    )
}