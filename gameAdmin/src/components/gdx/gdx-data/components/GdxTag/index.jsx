import { useState, useEffect, forwardRef } from "react";

import { Chip } from "@mui/material";




const GdxTag = forwardRef(({ color, variant, children, ...restProps }, ref) => {
    const [sx, setSx] = useState();
    const [c, setC] = useState();
    useEffect(() => {
        if (!color) {
            setC('default');
            return;
        }
        if (['default', 'primary', 'secondary', 'info', 'success', 'warning', 'error'].indexOf(color) < 0) {
            setSx({ backgroundColor: color, color: 'white' });
            setC('primary');
        } else {
            setC(color);
        }
    }, [color])
    return <Chip label={children} color={c} variant={variant || "outlined"} sx={sx} {...(restProps)} />
    // return (
    //     <Label
    //         ref={ref}
    //         variant={variant || "soft"}
    //         color={
    //             c
    //         }
    //         style={{margin:2}}
    //         sx={sx}
    //         {...restProps}
    //     >
    //         {children}
    //     </Label>
    // )
});

export default GdxTag;