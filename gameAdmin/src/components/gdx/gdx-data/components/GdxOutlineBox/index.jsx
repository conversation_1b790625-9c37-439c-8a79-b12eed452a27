import classNames from 'classnames';
import styles from './index.module.scss'





export default function GdxOutlineBox({ size, label, children }) {
    return (
        <div className={styles.mainContainer}>
            <div className={styles.header}>
                <div className={styles.headerBorderBefore} />
                {(label) && (
                    <div className={classNames(styles.headerTitle, size === 'small' && styles.small)}>
                        <span className={styles.title}>{label}</span>
                    </div>
                )}
                <div className={styles.headerBorderAfter} />
            </div>
            <div className={styles.childrenContainer}>{children}</div>
        </div>
    )
}