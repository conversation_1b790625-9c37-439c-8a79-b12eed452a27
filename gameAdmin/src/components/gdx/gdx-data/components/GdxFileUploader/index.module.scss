.root {
    .images {
        display: flex;
        flex-direction: column;

        .image {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px;
            
            .delete {
                display: flex;
            }
        }

        .opts {
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 4px 5px;

            .uploadBtn,
            .copyBtn {
                width: 100%;
            }

            .uploadBtn {
                margin-right: 1rem;
                position: relative;

                input {
                    position: absolute;
                    opacity: 0;
                    display: block;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 9;
                }
            }
        }
    }

    .pasteInput {
        input::placeholder {
            font-size: 11px;
            text-align: center;
        }
        width: 100%;
    }
}