import { useCallback, useEffect, useRef, useState } from 'react';

import { LoadingButton } from '@mui/lab';
import { Button, IconButton, Input } from '@mui/material';

import request from 'src/utils/request';

import { Iconify } from 'src/components/iconify';
import styles from './index.module.scss';

import GdxOutlineBox from '../GdxOutlineBox';
import { useLangTranslate } from '../../context/common';
import { getResourceName, getResourcePath } from '../../const';

export default function GdxFileUploader({
  label,
  size,
  value,
  onChange,
  multiple = false,
  ...restProps
}) {
  const [v, setV] = useState([]);
  useEffect(() => {
    if (multiple) {
      setV(value || []);
    } else {
      setV(value ? [value] : []);
    }
  }, [value, multiple]);

  const change = useCallback(
    (nv) => {
      if (!onChange) return;
      if (multiple) {
        onChange(nv);
      } else {
        onChange(nv && nv.length ? nv[nv.length - 1] : null);
      }
    },
    [onChange, multiple]
  );

  return (
    <GdxOutlineBox label={label} size={size}>
      <GdxFileUploaderInside
        size={size}
        value={v}
        onChange={change}
        multiple={multiple}
        {...restProps}
      ></GdxFileUploaderInside>
    </GdxOutlineBox>
  );
}

function GdxFileUploaderInside({ value, onChange, multiple = false }) {
  const fileRef = useRef();
  const { rl } = useLangTranslate();
  const [loading, setLoading] = useState(false);

  const loadFile = useCallback(
    (file) =>
      new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
      }),
    []
  );
  const uploadFile = useCallback(
    async (fileList) => {
      const v = value || [];
      setLoading(true);
      for (let i = 0; i < fileList.length; i += 1) {
        const f = fileList[i];
        const data = await loadFile(f);
        const ii = data.indexOf(',');
        const payload = { data: data.substring(ii + 1), fileName: f.name };
        const resp = await request('/api/file/upload_base64', payload);
        if (resp.status === 0) {
          if (v.indexOf(resp.model.id) < 0) {
            v.push(resp.model.id);
            if (onChange) {
              onChange([].concat(v));
            }
          }
        }
      }
      fileRef.current.value = '';
      setLoading(false);
    },
    [value, loadFile, onChange]
  );
  return (
    <div className={styles.root}>
      <div className={styles.images}>
        {(value || []).map((x, index) => (
          <div key={index} className={styles.image}>
            <Button
              className={styles.fileName}
              onClick={() => {
                window.open(`${getResourcePath(x)}`);
              }}
              variant="soft"
              startIcon={<Iconify icon="typcn:attachment-outline"></Iconify>}
            >
              {getResourceName(x)}
            </Button>
            <div className={styles.delete}>
              <IconButton
                size="large"
                color="error"
                onClick={() => {
                  if (onChange) {
                    onChange(value.filter((y) => y !== x));
                  }
                }}
              >
                <Iconify icon="material-symbols:delete"></Iconify>
              </IconButton>
            </div>
          </div>
        ))}
        <div className={styles.opts}>
          <div className={styles.uploadBtn}>
            <LoadingButton
              fullWidth
              loading={loading}
              variant="contained"
              color="primary"
              startIcon={<Iconify icon="material-symbols:upload"></Iconify>}
            >
              {rl('Upload')}
              <input
                ref={fileRef}
                title=""
                type="file"
                multiple={multiple}
                onChange={async (e) => {
                  if (!e.target.files) return;
                  await uploadFile(e.target.files);
                }}
                accept="*"
              />
            </LoadingButton>
          </div>
          <div className={styles.copyBtn}>
            <PasteButton
              loading={loading}
              setLoading={setLoading}
              onPaste={async (f) => {
                await uploadFile([f]);
              }}
            ></PasteButton>
          </div>
        </div>
      </div>
    </div>
  );
}

function PasteButton({ loading, onPaste }) {
  const { rl } = useLangTranslate();
  const [pasteMode, setPasteMode] = useState(false);
  return (
    <>
      {!pasteMode && (
        <LoadingButton
          loading={loading}
          onClick={() => {
            setPasteMode(true);
          }}
          variant="contained"
          color="secondary"
          fullWidth
          startIcon={<Iconify icon="clarity:paste-solid"></Iconify>}
        >
          {rl('Paste')}
        </LoadingButton>
      )}
      {pasteMode && (
        <Input
          onPaste={async (e) => {
            if (e.clipboardData) {
              e.preventDefault();
              for (let i = 0; i < e.clipboardData.items.length; i += 1) {
                const c = e.clipboardData.items[i];
                if (c.kind === 'file') {
                  setPasteMode(false);
                  const f = c.getAsFile();
                  await onPaste(f);
                }
              }
            }
          }}
          className={styles.pasteInput}
          autoFocus
          onBlur={() => {
            setPasteMode(false);
          }}
          placeholder={`${rl('Paste')} CTRL+V`}
        ></Input>
      )}
    </>
  );
}
