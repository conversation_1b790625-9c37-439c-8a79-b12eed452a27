import { useState, useEffect, useCallback } from "react";

import { ClearIcon } from "@mui/x-date-pickers";
import { Select, MenuItem, IconButton, InputLabel, FormControl } from "@mui/material";

import { GdxItemMdWidth } from "../../const";
import GdxAutocomplete from "../GdxAutocomplete";
import { useGdxTypeData } from "../../context/hook";


export default function GdxSelect({ canInput = true, ...restProps }) {
    if (canInput) return <GdxAutocomplete {...restProps} />;
    return <GdxDefaultSelect {...restProps} />
}



export function GdxDefaultSelect({ label, mdWidth, allowEmpty = true, dataType, value, onChange, options, disabled, ...restProps }) {
    const [opts, setOpts] = useState();
    const { typeData } = useGdxTypeData({ type: dataType });
    const getName = useCallback((id) => {
        if (opts) {
            const opt = opts.find((x) => x.id === id);
            if (opt) {
                return opt.name;
            }
        }
        return id;
    }, [opts])
    useEffect(() => {
        if (typeData && typeData.options) {
            setOpts(typeData.options);
        } else if (options) {
            setOpts(options);
        }
    }, [options, typeData])
    return (
        <FormControl
            sx={{
                flexShrink: 0,
                width: { xs: 1, md: mdWidth || GdxItemMdWidth },
            }}
            {...restProps}
        >
            <InputLabel id="selectLabelId">{label}</InputLabel>

            <Select
                value={value || ''}
                onChange={(e) => {
                    if (onChange) {
                        onChange(e.target.value || null);
                    }
                }}

                endAdornment={allowEmpty && !disabled && <IconButton size="small" sx={{ display: value ? "" : "none" }} onClick={() => {
                    if (onChange) onChange(null);
                }}><ClearIcon /></IconButton>}
                label={label}
                labelId="selectLabelId"
                renderValue={(selected) => getName(selected)}
                sx={{ textTransform: 'capitalize', "& .MuiSelect-iconOutlined": allowEmpty && { display: value ? 'none' : '' } || {} }}
                MenuProps={{ sx: { maxHeight: { md: '30rem' } } }}
                disabled={disabled}
            >
                {(opts || []).map((option) => (
                    <MenuItem key={option.id} value={option.id} selected={option.id === value}>
                        {option.name}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    )
}