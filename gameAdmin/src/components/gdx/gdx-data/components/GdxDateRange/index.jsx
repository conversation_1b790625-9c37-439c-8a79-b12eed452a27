import { Stack } from "@mui/material";

import GdxDatePicker from "../GdxDatePicker";



export default function GdxDateRange({ label, value, onChange, ...restProps }) {
    return (
        <Stack direction="row" spacing={0} sx={{ width: 1 }}>
            <GdxDatePicker label={`${label} start`} value={value?.start} onChange={(v) => onChange?.({ ...value, start: v })} />
            <GdxDatePicker label={`${label} finish`} value={value?.finish} onChange={(v) => onChange?.({ ...value, finish: v })} />
        </Stack>
    )
}