.root {
    .images {
        display: flex;
        flex-wrap: wrap;

        .image {
            border: 1px solid #d9d9d9;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            margin-right: 4px;
            margin-bottom: 4px;
            position: relative;

            .delete {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                align-items: center;
                justify-content: center;
                z-index: 1;
                display: none;
                background-color: rgba(0, 0, 0, 0.353);
            }

            &:hover {
                .delete {
                    display: flex;
                }
            }
        }

        .opts {
            border: 1px solid #d9d9d9;
            width: 100px;
            height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-around;
            padding: 4px 5px;

            .uploadBtn,
            .copyBtn {
                width: 100%;
            }

            .uploadBtn {
                position: relative;

                input {
                    position: absolute;
                    opacity: 0;
                    display: block;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 9;
                }
            }
        }
    }

    .pasteInput {
        input::placeholder {
            font-size: 11px;
            text-align: center;
        }
    }
}