import { Box, Card, Checkbox, CardMedia, CardHeader } from "@mui/material";

import GdxOutlineBox from "../GdxOutlineBox";
import { getResourceImageUrl } from "../../const";
import { useGdxTypeData } from "../../context/hook";



export default function GdxGridSelect({ label, column = 3, size, dataKey, value, onChange }) {
    const { typeData } = useGdxTypeData({ type: dataKey });
    if (!typeData || !typeData.options) return null;
    const { options } = typeData;
    return (
        <GdxOutlineBox label={label} size={size}>
            <Box
                rowGap={3}
                columnGap={2}
                display="grid"
                gridTemplateColumns={{
                    xs: 'repeat(2, 1fr)',
                    sm: `repeat(${column}, 1fr)`,
                }}
            >
                {
                    options.map((opt) => (<Card key={opt.id} sx={{ maxWidth: 345 }}>
                        <CardHeader
                            action={
                                <Checkbox checked={value === opt.id} onClick={() => {
                                    if (onChange) {
                                        if (value === opt.id) {
                                            onChange(null);
                                        } else {
                                            onChange(opt.id);
                                        }
                                    }
                                }} />
                            }
                            title={opt.name}
                        />
                        <CardMedia
                            component="img"
                            image={opt.images ? getResourceImageUrl(opt.images[0]) : getResourceImageUrl(opt.image)}
                            alt={opt.name}
                        />
                    </Card>))}
            </Box>
        </GdxOutlineBox>
    )
}