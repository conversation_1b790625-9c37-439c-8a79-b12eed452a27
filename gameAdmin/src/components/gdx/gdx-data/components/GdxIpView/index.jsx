import { useState, useEffect } from "react";

import { Button } from "@mui/material";

import GdxCopy from "../GdxCopy";



export default function GdxIpView({ value, row }) {
    const [v, setV] = useState('');
    const { ipIso } = row || {};
    useEffect(() => {
        setV(value);
    }, [value])
    return (
        <GdxCopy value={value}><Button>{v}{ipIso ? <span style={{ color: 'red', fontSize: 10, fontWeight: 100 }}>({ipIso})</span> : null}</Button></GdxCopy>
    )
}