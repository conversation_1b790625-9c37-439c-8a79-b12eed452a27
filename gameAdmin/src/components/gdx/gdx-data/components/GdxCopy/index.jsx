import { CopyToClipboard } from 'react-copy-to-clipboard'

import { toast } from 'src/components/snackbar';

import { useLangTranslate } from '../../context/common';



export default function GdxCopy({ value, children, onCopy }) {
    const { rl } = useLangTranslate();
    return (
        <CopyToClipboard text={value} onCopy={() => {
            if (onCopy) {
                onCopy();
                return;
            }
            toast.success(`${rl('Copy success')}!`)
        }}>
            {children}
        </CopyToClipboard>
    )
}