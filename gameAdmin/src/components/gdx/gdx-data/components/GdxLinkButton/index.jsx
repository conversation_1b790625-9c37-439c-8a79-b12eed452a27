import { Button, ButtonGroup } from '@mui/material';
import { Iconify } from 'src/components/iconify';

export default function GdxLinkButton({ value }) {
  const click = (link) => {
    if (link.indexOf('http') < 0) {
      link = `http://${link}`;
    }
    window.open(link, '_blank');
  };

  return (
    <ButtonGroup orientation="vertical" variant="text">
      {Array.isArray(value) ? (
        value.map((item) => (
          <Button
            key={item}
            sx={{ justifyContent: 'flex-start' }}
            startIcon={<Iconify icon="tdesign:link" />}
            onClick={() => {
              click(item);
            }}
          >
            {item}
          </Button>
        ))
      ) : (
        <Button
          startIcon={<Iconify icon="tdesign:link" />}
          onClick={() => {
            click(value);
          }}
        >
          {value}
        </Button>
      )}
    </ButtonGroup>
  );
}
