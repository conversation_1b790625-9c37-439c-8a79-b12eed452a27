import { useState, useEffect, useCallback } from "react";

import { ClearIcon } from "@mui/x-date-pickers";
import { Select, Checkbox, MenuItem, IconButton, InputLabel, FormControl } from "@mui/material";

import { GdxItemMdWidth } from "../../const";
import { useGdxTypeData } from "../../context/hook";



export default function GdxMultiSelect({ label, mdWidth, dataType, value, onChange, options, ...restProps }) {
    const [opts, setOpts] = useState();
    const { typeData } = useGdxTypeData({ type: dataType });
    const getName = useCallback((id) => {
        if (opts) {
            const opt = opts.find((x) => x.id === id);
            if (opt) {
                return opt.name;
            }
        }
        return id;
    }, [opts])
    useEffect(() => {
        if (typeData && typeData.options) {
            setOpts(typeData.options);
        } else if (options) {
            setOpts(options);
        }
    }, [options, typeData])
    return (
        <FormControl
            sx={{
                flexShrink: 0,
                width: { xs: 1, md: mdWidth || GdxItemMdWidth },
            }}
            {...restProps}
        >
            <InputLabel id="selectLabelId">{label}</InputLabel>

            <Select
                multiple
                value={value || []}
                labelId="selectLabelId"
                label={label}
                onChange={(e) => {
                    if (onChange) {
                        onChange(e.target.value);
                    }
                }}
                endAdornment={<IconButton size="small" sx={{ display: value && value.length ? "" : "none" }} onClick={() => {
                    if (onChange) onChange(null);
                }}><ClearIcon /></IconButton>}
                renderValue={(selected) => selected.map((x) => getName(x)).join(', ')}
                sx={{ textTransform: 'capitalize', "& .MuiSelect-iconOutlined": { display: value && value.length ? 'none' : '' } }}
                MenuProps={{ sx: { maxHeight: { md: '30rem' } } }}
            >

                {(opts || []).map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                        <Checkbox disableRipple size="small" checked={(value || []).includes(option.id)} />
                        {option.name}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    )
}