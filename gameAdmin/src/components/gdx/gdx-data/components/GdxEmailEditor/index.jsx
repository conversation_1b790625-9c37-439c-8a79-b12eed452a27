import { useRef, useState } from "react";
import EmailEditor from 'react-email-editor';

import { Box, But<PERSON>, Drawer } from "@mui/material";

import GdxOutlineBox from "../GdxOutlineBox";
import { getDialogZIndex } from "../../const";
import { useLangTranslate } from "../../context/common";



export default function GdxEmailEditor({ value, onChange, label, sx, size = 'medium', showClear = false, ...restProps }) {
    const [open, setOpen] = useState(false);
    const emailEditorRef = useRef(null);
    const { rl } = useLangTranslate();
    const handleDrawerOpen = () => {
        setOpen(true);
    };

    const handleDrawerClose = () => {
        setOpen(false);
    };

    const onSave = async () => {
        emailEditorRef.current?.editor.exportHtml((data) => {
            const { design, html } = data;
            const vv = { html, json: JSON.stringify(design) };
            if (onChange) onChange(vv);
            setOpen(false);
        });
    };

    const onLoad = (v) => {
        if (!emailEditorRef.current || !emailEditorRef.current?.editor) {
            return;
        }
        if (v && v.json) {
            emailEditorRef.current.editor.loadDesign(JSON.parse(v.json));
        }
    };

    const onReady = () => {
        onLoad(value);
    };

    return (
        <GdxOutlineBox label={label} size={size}>
            <Button color="primary" variant="outlined" onClick={handleDrawerOpen}>{rl('Open Editor')}</Button>
            <Drawer anchor="right" open={open} onClose={handleDrawerClose} sx={{ zIndex: getDialogZIndex() }} >
                <Box sx={{ padding: 3, display: 'flex', justifyContent: 'flex-end', background: "#fff" }}>
                    <Button variant="outlined" onClick={() => { setOpen(false); }} sx={{ marginRight: 2 }}>{rl('Cancel')}</Button>
                    <Button variant="contained" onClick={onSave}>{rl('Save')}</Button>
                </Box>
                <EmailEditor ref={emailEditorRef} onLoad={() => { onLoad(value) }} onReady={onReady} />
            </Drawer>
        </GdxOutlineBox>
    )
}