import { useState, useEffect } from "react";

import { Button } from "@mui/material";

import GdxCopy from "../GdxCopy";



export default function GdxIdView({ value }) {
    const [v, setV] = useState('');
    useEffect(() => {
        if (value.length > 8) {
            setV(`${value.substring(0, 2)}****${value.substring(value.length - 2)}`);
        } else {
            setV(value);
        }
    }, [value])
    return (
        <GdxCopy value={value}><Button>{v}</Button></GdxCopy>
    )
}