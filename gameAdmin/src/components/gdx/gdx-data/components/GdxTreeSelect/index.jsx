import { useState, useEffect, useCallback } from 'react';

import { Checkbox } from '@mui/material';
import { TreeItem, TreeView } from '@mui/x-tree-view';

import { Iconify } from 'src/components/iconify';
import Scrollbar from 'src/components/scrollbar';

import GdxOutlineBox from '../GdxOutlineBox';
import { useGdxTypeData } from '../../context/hook';

function buildPermissionTree(options) {
  const ret = options.filter((x) => x.root);
  _buildSubTree(ret, options);
  return ret;
}

function _buildSubTree(ps, options) {
  ps.forEach((p) => {
    p.children = options.filter((x) => x.parentId === p.id);
    p.children.forEach((c) => {
      c.parent = p;
    });
    _buildSubTree(p.children, options);
  });
}

export default function GdxTreeSelect({
  label,
  size,
  mdWidth,
  dataType,
  value,
  onChange,
  options,
  ...restProps
}) {
  const [opts, setOpts] = useState();
  const [trees, setTrees] = useState([]);
  const { typeData } = useGdxTypeData({ type: dataType });

  useEffect(() => {
    if (typeData && typeData.options) {
      setOpts(typeData.options);
    } else if (options) {
      setOpts(options);
    }
  }, [options, typeData]);

  useEffect(() => {
    if (opts && opts.length) {
      const permissionTree = buildPermissionTree(opts);
      setTrees(permissionTree);
    }
  }, [opts]);

  const renderTree = useCallback(
    (nodes) => {
      const isNodeChecked = (value || []).includes(nodes.id);

      const isEveryDescendantChecked = (children, vs) =>
        children?.every(
          (child) => (vs || []).includes(child.id) && isEveryDescendantChecked(child.children, vs)
        ) || false;

      const isSomeDescendantChecked = (children, vs) =>
        children?.some(
          (child) => (vs || []).includes(child.id) || isSomeDescendantChecked(child.children, vs)
        ) || false;

      const getAllDescendantIds = (children) => {
        let ret = [];
        for (const c of children) {
          ret.push(c.id);
          if (c.children) {
            const cc = getAllDescendantIds(c.children);
            ret = ret.concat(cc);
          }
        }
        return ret;
      };

      const checked =
        nodes.children && nodes.children.length
          ? isEveryDescendantChecked(nodes.children, value)
          : isNodeChecked;

      return (
        <TreeItem
          key={nodes.id}
          nodeId={nodes.id}
          label={
            <div>
              <Checkbox
                indeterminate={isSomeDescendantChecked(nodes.children, value) && !checked}
                size="small"
                checked={checked}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  let nv = value || [];
                  const selected = nv.indexOf(nodes.id) < 0;
                  if (selected) {
                    nv.push(nodes.id);
                    if (nodes.children) {
                      const cc = getAllDescendantIds(nodes.children);
                      for (const c of cc) {
                        if (nv.indexOf(c) < 0) {
                          nv.push(c);
                        }
                      }
                    }
                    let p = nodes.parent;
                    while (p) {
                      if (nv.indexOf(p.id) < 0) {
                        nv.push(p.id);
                      }
                      p = p.parent;
                    }
                  } else {
                    nv = nv.filter((x) => x !== nodes.id);
                    if (nodes.children) {
                      const cc = getAllDescendantIds(nodes.children);
                      nv = nv.filter((x) => cc.indexOf(x) < 0);
                    }
                    let p = nodes.parent;
                    while (p) {
                      if (!isSomeDescendantChecked(p.children, nv)) {
                        nv = nv.filter((x) => x !== p.id);
                      }
                      p = p.parent;
                    }
                  }
                  if (onChange) {
                    onChange(nv);
                  }
                }}
              />
              {nodes.name}
            </div>
          }
        >
          {Array.isArray(nodes.children) ? nodes.children.map((node) => renderTree(node)) : null}
        </TreeItem>
      );
    },
    [value, onChange]
  );

  return (
    <GdxOutlineBox
      label={
        <>
          {label} ({(value || []).length})
        </>
      }
      size={size}
    >
      <Scrollbar sx={{ maxHeight: 400 }}>
        <TreeView
          defaultCollapseIcon={<Iconify icon="dashicons:arrow-down" />}
          defaultExpandIcon={<Iconify icon="dashicons:arrow-right" />}
          defaultEndIcon={<div style={{ width: 20 }} />}
        >
          {(trees || []).map((option) => renderTree(option))}
        </TreeView>
      </Scrollbar>
    </GdxOutlineBox>
  );
}
