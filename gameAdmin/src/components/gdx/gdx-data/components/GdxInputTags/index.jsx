import { useState, useEffect, useCallback } from 'react';

import { Box, Button } from '@mui/material';

import GdxInput from '../GdxInput';
import GdxOutlineBox from '../GdxOutlineBox'
import { useLangTranslate } from '../../context/common';



export default function GdxInputTags({ size, label, value, onChange, ...restProps }) {
    const [data, setData] = useState([]);
    const { rl } = useLangTranslate();
    const [showAddButton, setShowAddButton] = useState(true);

    const handleDataChange = useCallback((i, v) => {
        setShowAddButton(true);
        const newData = [...data];
        if (v === '' || !v) {
            newData.splice(i, 1);
        } else {
            newData[i] = v;
        }
        setData(newData);
        if (onChange) onChange(newData);
    }, [onChange, data]);

    const handleAddData = () => {
        setShowAddButton(false);
        setData([...data, '']);
    };

    useEffect(() => {
        setData(value || []);
    }, [value])

    return (
        <GdxOutlineBox label={label} size={size}>
            <Box display='grid' gridTemplateColumns='repeat(2, 1fr)' gap={1}>
                {data.map((item, index) => (
                    <GdxInput key={index} value={item} size='small'
                        onChange={(v) => handleDataChange(index, v)}
                        showClear
                        {...restProps}></GdxInput>
                ))}
                {showAddButton && <Button size={size} variant="outlined" onClick={handleAddData} >{rl('Add')}</Button>}
            </Box>
        </GdxOutlineBox>
    )
}