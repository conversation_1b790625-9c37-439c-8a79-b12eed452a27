import { useState } from 'react';

import { Tab, Tabs } from '@mui/material';

import { Iconify } from 'src/components/iconify';

export default function GdxTabs({ tabs }) {
  const [currentTab, setCurrentTab] = useState(tabs && tabs[0] && tabs[0].id);
  const tt = tabs.find((x) => x.id === currentTab);
  return (
    <>
      <Tabs
        value={currentTab}
        onChange={(e, nv) => {
          setCurrentTab(nv);
        }}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      >
        {tabs.map((tab) => (
          <Tab
            key={tab.id}
            label={tab.label}
            icon={tab.icon && <Iconify icon={tab.icon} width={24} />}
            value={tab.id}
          />
        ))}
      </Tabs>
      {tt && tt.render()}
    </>
  );
}
