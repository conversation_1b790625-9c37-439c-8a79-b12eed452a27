import { useState } from "react";

import { LoadingButton } from "@mui/lab";

export default function GdxButton({ onClick, ...restProps }) {
    const [loading, setLoading] = useState(false);
    return (
        <LoadingButton loading={loading} onClick={async () => {
            setLoading(true);
            if (onClick) {
                await onClick();
            }
            setLoading(false);
        }} {...restProps} />
    )
}