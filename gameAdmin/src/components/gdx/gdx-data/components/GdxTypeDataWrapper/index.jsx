import { useMemo, useState, useEffect } from "react";

import request from "src/utils/request";

import { useRemoteTableContext } from "src/components/gdx/remote-table/context/hook";

import { useGdxTypeData } from "../../context/hook";
import { useLangTranslate } from "../../context/common";

export default function GdxTypeDataWrapper({ type, allFlag, render }) {
    const { rl } = useLangTranslate();
    const { typeData } = useGdxTypeData({ type });
    const opts = useMemo(() => {
        let ret = allFlag ? [{ name: rl('ALL') }] : [];
        ret = ret.concat(typeData && typeData.options || []);
        return ret;
    }, [typeData, allFlag, rl]);
    return render({ options: opts });
}

export function GdxTypeDataValueCount({ render, fetchCountUrl, field, value, countQuery }) {
    const [count, setCount] = useState();
    const { lastChangeTime } = useRemoteTableContext();
    useEffect(() => {
        const loadCount = async () => {
            const resp = await request(fetchCountUrl, { query: { [field]: value, ...countQuery } });
            if (resp.status === 0) {
                setCount(resp.model);
            }
        }
        loadCount();
    }, [fetchCountUrl, field, value, lastChangeTime, countQuery]);
    if (count === undefined) return null;
    return render({ count })
}