import { Drawer, IconButton, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { getDialogZIndex } from '../../const';

export default function GdxDrawer({ open = false, title, children, onClose, width = 600 }) {
  return (
    <Drawer
      sx={{ zIndex: getDialogZIndex() }}
      PaperProps={{
        sx: { width, maxWidth: 1, padding: 0 },
      }}
      open={open}
      onClose={() => {
        if (onClose) onClose();
      }}
      anchor="right"
    >
      <>
        <div
          style={{
            background: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 10,
            padding: '1rem',
          }}
        >
          <Typography variant="h6" noWrap component="div">
            {title}
          </Typography>
          <div>
            <IconButton onClick={onClose}>
              <Iconify icon="mingcute:close-fill" width={24} />
            </IconButton>
          </div>
        </div>
        <div style={{ padding: '1rem' }}>{children}</div>
      </>
    </Drawer>
  );
}
