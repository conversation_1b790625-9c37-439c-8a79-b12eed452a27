import { merge } from "lodash";
import React, { useMemo, useState, useEffect, useReducer, useCallback } from "react";

import request from "src/utils/request";
import { objectFilterEmpty } from "src/utils/utils";

import { RemoteTableContext } from "./context";



const initialState = {
    inited: false,
    loading: false,
    pageNo: 1,
    pageSize: 5,
    data: [],
    filter: {},
    totalElements: 0,
    totalPages: 0,
    dirty: false,
    orderBy: undefined,
    order: undefined,
    editRecord: undefined,
    openFilterPanel: false,
    lastChangeTime: 0,
    expandIds: [],
};

const reducer = (state, action) => {
    const { type, payload } = action;
    if (type === 'nextPage') {
        if (state.pageNo < state.totalPages) {
            return {
                ...state,
                pageNo: state.pageNo + 1,
                dirty: !state.loading
            }
        }
    } else if (type === 'prevPage') {
        if (state.pageNo > 1) {
            return {
                ...state,
                pageNo: state.pageNo - 1,
                dirty: !state.loading
            }
        }
    } else if (type === 'goPage') {
        if (payload.pageNo >= 1 && payload.pageNo <= state.totalPages) {
            if (payload.pageSize && payload.pageSize !== state.pageSize) {
                payload.pageNo = 1;
            }
            return {
                ...state,
                pageNo: payload.pageNo,
                pageSize: payload.pageSize || state.pageSize,
                dirty: !state.loading
            }
        }
    } else if (type === 'filter') {
        return {
            ...state,
            filter: { ...payload.filter },
            dirty: !state.loading
        }
    } else if (type === 'setOrder') {
        return {
            ...state,
            ...payload,
            dirty: !state.loading
        }
    } else {
        return {
            ...state,
            ...payload
        }
    }
    return state;
};

export function RemoteTableProvider({ children, options }) {
    const [state, dispatch] = useReducer(reducer, { ...merge({}, initialState), pageSize: options.defaultPageSize || 10, orderBy: options.orderBy, order: options.order, options: { rowHeight: 80, ...options } });
    const apiRequest = options.requestHook && options.requestHook.apiPost || request;
    const [expandIds, setExpandIds] = useState([]);
    const reload = useCallback(async () => {
        if (state.loading || state.openFilterPanel) return;
        if (options.statusFilter && !options.statusFilter.showAll && (!state.filter || !state.filter[options.statusFilter.id])) {
            return;
        }
        dispatch({ type: 'loading', payload: { loading: true } });
        let query = {
            ...objectFilterEmpty(state.filter),
            pageNo: state.pageNo, pageSize: state.pageSize,
            ...objectFilterEmpty(options.fixedQuery || {}),
            sortFields: state.orderBy ? [{ field: state.orderBy, sort: state.order === 'desc' ? -1 : 1 }] : options.fixedQuery?.sortFields || [],
        };
        if (options.onPageBefore) {
            const nquery = options.onPageBefore(query);
            if (nquery) {
                query = nquery;
            }
        }
        const resp = await apiRequest(`${options.baseUrl}/page`, {
            query
        });
        if (resp.status === 0) {
            const { page } = resp;
            dispatch({ type: 'pageSuccess', payload: { dirty: false, inited: true, loading: false, data: page.content, pageNo: page.number, pageSize: page.size, totalPages: page.totalPages, totalElements: page.totalElements } });
        } else {
            dispatch({ type: 'pageFailed', payload: { dirty: false, loading: false } });
        }
    }, [state, options])
    const reloadRows = useCallback(async (ids) => {
        if (!ids || !ids.length) return;
        const resp = await apiRequest(`${options.baseUrl}/page`, {
            query: {
                ids
            }
        });
        if (resp.status === 0) {
            const nd = [].concat(state.data);
            for (const item of resp.page.content) {
                const index = nd.findIndex((x) => (x.id === item.id));
                if (index >= 0) {
                    nd[index] = item;
                }
            }
            dispatch({ type: 'other', payload: { data: nd } });
        }
    }, [state, options])
    const updateFilter = useCallback((filter) => {
        dispatch({ type: 'filter', payload: { filter } });
    }, [])
    const nextPage = useCallback(() => {
        dispatch({ type: 'nextPage' })
    }, [])
    const prevPage = useCallback(() => {
        dispatch({ type: 'prevPage' })
    }, [])
    const goPage = useCallback((pageNo, pageSize) => {
        dispatch({ type: 'goPage', payload: { pageNo, pageSize } })
    }, [])
    const setOrder = useCallback((orderBy, order) => {
        dispatch({ type: 'setOrder', payload: { orderBy, order } });
    }, [])
    const setSelectedRowIds = useCallback((ids) => {
        const allIds = state.data.map((x) => x.id);
        dispatch({ type: 'setSelectedRowIds', payload: { selectedRowIds: (ids || []).filter(x => allIds.indexOf(x) >= 0) } });
    }, [state])
    const isSelected = useCallback((id) => state.selectedRowIds && state.selectedRowIds.indexOf(id) >= 0 && true || false, [state]);
    const setSelectedRowId = useCallback((id) => {
        if (state.selectedRowIds && state.selectedRowIds.indexOf(id) >= 0) {
            setSelectedRowIds(state.selectedRowIds.filter((x) => x !== id));
        } else {
            setSelectedRowIds((state.selectedRowIds || []).concat(id));
        }
    }, [state.selectedRowIds, setSelectedRowIds])
    const setEditAction = useCallback((action, record, extra = null) => {
        dispatch({ type: 'setEditAction', payload: { editAction: action, editRecord: record ? merge({}, record) : null, editExtra: extra } });
    }, [])
    const setOpenFilterPanel = useCallback((v) => {
        dispatch({ type: 'openFilterPanel', payload: { openFilterPanel: v } })
    }, [])
    const persist = useCallback(async (model, rowIndex = -1) => {
        model = merge({}, { ...state.editRecord, ...model }, options.fixedPersist || {});
        if (options.onSave) {
            model = await options.onSave(model, state);
            return model;
        }
        if (options.onPersist) {
            model = options.onPersist(model, state);
        }
        const resp = await apiRequest(`${options.baseUrl}/persist`, { model, _rowIndex: rowIndex });
        if (resp.status === 0) {
            dispatch({ type: 'persist', payload: { lastChangeTime: new Date().getTime() } })
        }
        if (options.onPersistAfter) {
            options.onPersistAfter(model, state);
        }
        return resp;
    }, [state]);
    const batchUpdate = useCallback(async (data) => {
        const { selectedRowIds } = state;
        if (!selectedRowIds || !selectedRowIds.length) {
            throw new Error('missing selected rows');
        }
        const resp = await apiRequest(`${options.baseUrl}/batch_update`, { update: data, ids: selectedRowIds })
        if (resp.status === 0) {
            dispatch({ type: 'batchUpdate', payload: { lastChangeTime: new Date().getTime() } })
        }
        return resp;
    }, [state]);
    const deleteOne = useCallback(async (id, rowIndex) => {
        const resp = await apiRequest(`${options.baseUrl}/delete`, { id, _rowIndex: rowIndex });
        if (resp.status === 0) {
            dispatch({ type: 'delete', payload: { lastChangeTime: new Date().getTime() } })
        }
        if (options.onDeleteAfter) {
            options.onDeleteAfter({ id, rowIndex }, state);
        }
        return resp;
    }, [state]);

    const moveUp = useCallback(async (id, rowIndex) => {
        const resp = await apiRequest(`${options.baseUrl}/up`, { id, _rowIndex: rowIndex });
        if (resp.status === 0) {
            dispatch({ type: 'up', payload: { lastChangeTime: new Date().getTime() } })
        }
        return resp;
    }, [state]);

    const batchDelete = useCallback(async (ids) => {
        if (!ids || !ids.length) return { status: -1, message: 'missing selected rows' }
        const resp = await apiRequest(`${options.baseUrl}/batch_delete`, { ids });
        if (resp.status === 0) {
            dispatch({ type: 'batchDelete', payload: { lastChangeTime: new Date().getTime() } })
        }
        return resp;
    }, [state]);

    useEffect(() => {
        reload();
    }, [])
    useEffect(() => {
        if (state.dirty) {
            reload();
        }
    }, [state.dirty, reload])
    const memoizedValue = useMemo(() => {
        const allSelected = state.selectedRowIds && state.selectedRowIds.length && state.selectedRowIds.length === state.data.length && state.selectedRowIds.length === state.data.filter((x) => state.selectedRowIds.indexOf(x.id) >= 0).length && true || false;
        return {
            ...state,
            reload,
            updateFilter,
            nextPage,
            prevPage,
            goPage,
            setOrder,
            setSelectedRowIds,
            setSelectedRowId,
            isSelected,
            allSelected,
            setEditAction,
            setOpenFilterPanel,
            persist,
            deleteOne,
            batchUpdate,
            batchDelete,
            moveUp,
            partialSelected: !allSelected && state.selectedRowIds && state.selectedRowIds.length && true || false,
            options,
            expandIds, setExpandIds, reloadRows
        }
    }, [state, batchDelete, moveUp, deleteOne, batchUpdate, updateFilter, reload, reloadRows, nextPage, prevPage, persist,
        goPage, setOrder, setSelectedRowIds, setSelectedRowId,
        setEditAction, setOpenFilterPanel, options, expandIds, setExpandIds])

    return (
        <RemoteTableContext.Provider value={memoizedValue}>
            {children}
        </RemoteTableContext.Provider>
    )
}