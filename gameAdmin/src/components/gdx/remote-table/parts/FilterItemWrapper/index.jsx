import GdxInput from "src/components/gdx/gdx-data/components/GdxInput";
import GdxSelect from "src/components/gdx/gdx-data/components/GdxSelect";
import GdxDateRange from "src/components/gdx/gdx-data/components/GdxDateRange";
import GdxDatePicker from "src/components/gdx/gdx-data/components/GdxDatePicker";
import GdxMultiSelect from "src/components/gdx/gdx-data/components/GdxMultiSelect";
import GdxMonthPicker from "src/components/gdx/gdx-data/components/GdxMonthPicker";

import GdxFilterItem from "../GdxFilterItem";



export default function FilterItemWrapper({ f, ...restProps }) {
    let e;
    restProps = { ...restProps, ...f.props };
    if (f.type === "multi-select") {
        e = <GdxMultiSelect label={f.label} dataType={f.dataType} {...restProps} />
    } else if (f.type === 'date') {
        e = <GdxDatePicker label={f.label} {...restProps}></GdxDatePicker>
    } else if (f.type === 'month') {
        e = <GdxMonthPicker label={f.label} {...restProps}></GdxMonthPicker>
    } else if (f.type === 'select') {
        e = <GdxSelect label={f.label} dataType={f.dataType} {...restProps} />
    } else if (f.type === 'date-range') {
        e = <GdxDateRange label={f.label} {...restProps} />
    } else {
        e = <GdxInput label={f.label} {...restProps} />
    }
    return (
        <GdxFilterItem name={f.id} key={f.id}>
            {e}
        </GdxFilterItem>
    )
}