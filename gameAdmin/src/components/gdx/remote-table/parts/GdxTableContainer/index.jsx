import { Table, TableContainer } from '@mui/material';

import { Scrollbar } from 'src/components/scrollbar';
import { TableNoData, TableEmptyRows } from 'src/components/table';

import GdxTableBody from '../GdxTableBody';
import GdxTableHeader from '../GdxTableHeader';
import { useRemoteTableContext } from '../../context/hook';
import GdxTableSelectedActionBar from '../GdxTableSelectedActionBar';

function GdxTableEmptyRows() {
  const {
    pageSize,
    data,
    loading,
    inited,
    options: { size },
  } = useRemoteTableContext();
  if (!loading || !inited) return null;
  return <TableEmptyRows height={size === 'small' ? 52 : 72} emptyRows={pageSize - data.length} />;
}

function GdxTableNoData() {
  const { loading, inited, data } = useRemoteTableContext();
  const notFound = (inited && !loading && !data.length && true) || false;
  if (!notFound) return null;
  return <TableNoData notFound={notFound} />;
}

export default function GdxTableContainer() {
  const {
    options: { limitHeight, size },
  } = useRemoteTableContext();
  return (
    <TableContainer sx={{ position: 'relative', overflow: 'unset', height: limitHeight || 'auto' }}>
      <GdxTableSelectedActionBar></GdxTableSelectedActionBar>
      <Scrollbar>
        <Table size={size || 'small'} sx={{ maxWidth: 1 }}>
          <GdxTableHeader></GdxTableHeader>

          <GdxTableBody>
            <GdxTableEmptyRows></GdxTableEmptyRows>
            <GdxTableNoData></GdxTableNoData>
          </GdxTableBody>
        </Table>
      </Scrollbar>
    </TableContainer>
  );
}
