import { Checkbox, TableRow, TableCell, TableHead, TableSortLabel } from "@mui/material";

import { useRemoteTableContext } from "../../context/hook";

export default function GdxTableHeader() {
    const { options: { columns, batchActions, actions, expandRowRender }, allSelected, partialSelected, setOrder, order, orderBy, data, setSelectedRowIds } = useRemoteTableContext();
    return (
        <TableHead>
            <TableRow>
                {batchActions && batchActions.length && <TableCell padding="checkbox">
                    <Checkbox
                        indeterminate={partialSelected}
                        checked={allSelected}
                        onChange={(event) => {
                            if (event.target.checked) {
                                setSelectedRowIds(data.map(x => x.id));
                            } else {
                                setSelectedRowIds([]);
                            }
                        }}
                    />
                </TableCell> || null}

                {columns.map((headCell) => (
                    <TableCell
                        key={headCell.id}
                        align={headCell.align || 'left'}
                        sx={{ width: headCell.width, minWidth: headCell.minWidth || 100, whiteSpace: 'nowrap' }}
                    >
                        {headCell.sorter ? (
                            <TableSortLabel
                                hideSortIcon={false}
                                active={orderBy === headCell.id}
                                direction={order || (headCell.sortDirections ? headCell.sortDirections[0] : 'asc') || 'asc'}
                                onClick={() => {
                                    const sortDts = headCell.sortDirections ? headCell.sortDirections : ['asc', 'desc'];
                                    const index = sortDts.findIndex(x => x === order);
                                    if (index < 0) {
                                        setOrder(headCell.id, sortDts[0]);
                                    } else if (index === sortDts.length - 1) {
                                        setOrder('', false);
                                    } else {
                                        setOrder(headCell.id, sortDts[index + 1]);
                                    }
                                }}
                            >
                                {headCell.name}
                            </TableSortLabel>
                        ) : (
                            headCell.name
                        )}
                    </TableCell>
                ))}

                {(actions && actions.length || expandRowRender) && <TableCell></TableCell> || null}
            </TableRow>
        </TableHead>
    )
}