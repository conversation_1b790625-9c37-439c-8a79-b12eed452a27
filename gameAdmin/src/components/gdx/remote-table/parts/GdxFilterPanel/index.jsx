import { useMemo } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, IconButton, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { getDialogZIndex } from 'src/components/gdx/gdx-data/const';
import { useLangTranslate } from 'src/components/gdx/gdx-data/context/common';

import FilterItemWrapper from '../FilterItemWrapper';
import { useRemoteTableContext } from '../../context/hook';

export default function GdxFilterPanel() {
  const { rl } = useLangTranslate();
  const {
    openFilterPanel,
    setOpenFilterPanel,
    options: { filters, expandFilterStart },
  } = useRemoteTableContext();
  const filterPanel = useMemo(() => {
    if (!filters || !filters.length || !expandFilterStart || expandFilterStart <= 0) {
      return null;
    }
    return filters
      .slice(expandFilterStart)
      .map((f) => <FilterItemWrapper f={f} key={f.id}></FilterItemWrapper>);
  }, [filters, expandFilterStart]);
  return (
    <>
      <Button
        disableRipple
        color="inherit"
        endIcon={
          <Badge color="error" variant="dot" invisible>
            <Iconify icon="ic:round-filter-list" />
          </Badge>
        }
        onClick={() => {
          setOpenFilterPanel(true);
        }}
      >
        Filters
      </Button>

      <Drawer
        sx={{ zIndex: getDialogZIndex() }}
        anchor="right"
        open={openFilterPanel}
        onClose={() => {
          setOpenFilterPanel(false);
        }}
        slotProps={{
          backdrop: { invisible: true },
        }}
        PaperProps={{
          sx: { width: 280 },
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{ py: 2, pr: 1, pl: 2.5 }}
        >
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            {rl('Filters')}
          </Typography>

          {/* <Tooltip title="Reset">
                        <IconButton onClick={onResetFilters}>
                            <Badge color="error" variant="dot" invisible={false}>
                                <Iconify icon="solar:restart-bold" />
                            </Badge>
                        </IconButton>
                    </Tooltip> */}

          <IconButton
            onClick={() => {
              setOpenFilterPanel(false);
            }}
          >
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Stack>

        <Divider />

        <Scrollbar sx={{ px: 2.5, py: 3 }}>
          <Stack spacing={3}>{filterPanel}</Stack>
        </Scrollbar>
      </Drawer>
    </>
  );
}
