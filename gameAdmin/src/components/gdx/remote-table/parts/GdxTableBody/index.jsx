import { TableBody } from "@mui/material";

import { TableSkeleton } from "src/components/table";

import GdxTableRow from "../GdxTableRow";
import { useRemoteTableContext } from "../../context/hook";



export default function GdxTableBody({ children }) {
    const { loading, data, pageSize, options: { rowHeight } } = useRemoteTableContext();
    return (
        <TableBody>
            {loading ? (
                [...Array(pageSize)].map((i, index) => (
                    <TableSkeleton key={index} sx={{ height: rowHeight }} />
                ))
            ) : (
                <>
                    {data.map((row, index) => (
                        <GdxTableRow key={row.id || index} row={row} rowIndex={index} />
                    ))}
                </>
            )}
            {children}
        </TableBody>
    )
}