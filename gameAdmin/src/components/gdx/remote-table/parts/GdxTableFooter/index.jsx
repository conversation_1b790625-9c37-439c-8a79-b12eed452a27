import { Box, TablePagination } from '@mui/material';

import { useResponsive } from 'src/hooks/use-responsive';
import { useRemoteTableContext } from '../../context/hook';

export default function GdxTableFooter() {
  const {
    pageSize,
    pageNo,
    goPage,
    totalElements,
    options: { pages, size, hidePage, footerRender },
  } = useRemoteTableContext();
  const isMobile = useResponsive('down', 'md');
  if (hidePage) return null;
  return (
    <Box sx={{ position: 'relative' }}>
      {footerRender && footerRender()}
      {!footerRender && (
        <TablePagination
          size={size || 'medium'}
          rowsPerPageOptions={pages || [5, 10, 20, 50, 100]}
          page={pageNo - 1}
          rowsPerPage={pageSize}
          count={totalElements}
          onPageChange={(e, page) => {
            goPage(page + 1, pageSize);
          }}
          onRowsPerPageChange={(e) => {
            goPage(pageNo, parseInt(e.target.value, 10));
          }}
          component="div"
          sx={{
            borderTopColor: 'transparent',
          }}
          labelRowsPerPage={isMobile ? '' : '每页条数：'}
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} 条，共 ${count !== -1 ? count : `更多`} 条`
          }
          showFirstButton={!isMobile}
          showLastButton={!isMobile}
        />
      )}
    </Box>
  );
}
