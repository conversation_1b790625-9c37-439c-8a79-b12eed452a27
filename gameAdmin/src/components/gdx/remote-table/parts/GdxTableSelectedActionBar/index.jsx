import { useState, useEffect, useCallback } from 'react';

// import { Stack } from "@mui/system";
import { Stack, Tooltip, Checkbox, IconButton, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { useGdxDataContext } from 'src/components/gdx/gdx-data/context/hook';
import { useLangTranslate } from 'src/components/gdx/gdx-data/context/common';

import { useRemoteTableContext } from '../../context/hook';

export default function GdxTableSelectedActionBar({ sx }) {
  const { rl } = useLangTranslate();
  const [actionDatas, setActionDatas] = useState([]);
  const context = useRemoteTableContext();
  const { showConfirm } = useGdxDataContext();
  const {
    setSelectedRowIds,
    selectedRowIds,
    batchDelete,
    setEditAction,
    data,
    partialSelected,
    allSelected,
    reload,
    options: { batchActions, onBatchAction },
  } = context;
  const onBatchActionWrap = useCallback(
    async (act) => {
      if (!selectedRowIds || !selectedRowIds.length) return;
      if (onBatchAction) {
        if (await onBatchAction(act.id, context)) {
          return;
        }
      }
      if (act.id === 'delete') {
        showConfirm({
          title: rl('Delete'),
          content: rl('Are you sure want to delete?'),
          okText: rl('Delete'),
          okColor: 'error',
          onOk: async () => {
            const resp = await batchDelete(selectedRowIds);
            if (resp.status === 0) {
              reload();
              setSelectedRowIds([]);
            }
          },
        });
      } else if (act.id === 'edit') {
        setEditAction('batch_edit', {});
      }
    },
    [selectedRowIds]
  );
  useEffect(() => {
    const acts = batchActions?.map((x) => {
      if (x === 'delete') {
        x = {
          id: 'delete',
          icon: 'solar:trash-bin-trash-bold',
          color: 'error',
          title: rl('Delete'),
        };
      } else if (x === 'edit') {
        x = { id: 'edit', icon: 'solar:pen-bold', color: 'primary', title: rl('Edit') };
      }
      if (!x.color) {
        x.color = 'primary';
      }
      return x;
    });
    setActionDatas(acts);
  }, [batchActions]);
  if (!selectedRowIds || !selectedRowIds.length) return null;
  return (
    <Stack
      direction="row"
      alignItems="center"
      sx={{
        pl: 1,
        pr: 2,
        top: 0,
        left: 0,
        width: 1,
        zIndex: 9,
        height: 58,
        position: 'absolute',
        bgcolor: 'primary.lighter',
        ...sx,
      }}
    >
      <Checkbox
        indeterminate={partialSelected}
        checked={allSelected}
        onChange={(event) => {
          if (event.target.checked) {
            setSelectedRowIds(data.map((x) => x.id));
          } else {
            setSelectedRowIds([]);
          }
        }}
      />

      <Typography
        variant="subtitle2"
        sx={{
          ml: 2,
          flexGrow: 1,
          color: 'primary.main',
        }}
      >
        {(selectedRowIds && selectedRowIds.length) || 0} {rl('selected')}
      </Typography>

      <Stack direction="row">
        {actionDatas.map((ad) => (
          <Tooltip key={ad.id} title={ad.title}>
            <IconButton
              color={ad.color}
              onClick={() => {
                onBatchActionWrap(ad);
              }}
            >
              <Iconify icon={ad.icon} />
            </IconButton>
          </Tooltip>
        ))}
        {/* <Tooltip title="Sent">
                    <IconButton color="primary">
                        <Iconify icon="iconamoon:send-fill" />
                    </IconButton>
                </Tooltip>

                <Tooltip title="Download">
                    <IconButton color="primary">
                        <Iconify icon="eva:download-outline" />
                    </IconButton>
                </Tooltip>

                <Tooltip title="Print">
                    <IconButton color="primary">
                        <Iconify icon="solar:printer-minimalistic-bold" />
                    </IconButton>
                </Tooltip>

                <Tooltip title="Delete">
                    <IconButton color="primary">
                        <Iconify icon="solar:trash-bin-trash-bold" />
                    </IconButton>
                </Tooltip> */}
      </Stack>
    </Stack>
  );
}
