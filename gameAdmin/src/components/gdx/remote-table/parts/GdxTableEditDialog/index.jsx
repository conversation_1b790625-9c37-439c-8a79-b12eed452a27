import { toast } from "sonner";
import { useState } from "react";

import { Alert } from "@mui/material";

import GdxFormDialog from "src/components/gdx/gdx-form-dialog";
import { useLangTranslate } from "src/components/gdx/gdx-data/context/common";

import { useRemoteTableContext } from "../../context/hook";

export default function GdxTableEditDialog() {
    const { rl } = useLangTranslate();
    const [errorMsg, setErrorMsg] = useState('');
    const context = useRemoteTableContext();
    const { reload, editRecord, editAction, editExtra, setEditAction, batchUpdate, persist, options: { editFormColumn, editFormWidth, editForm, editFormValidators, editFormProps } } = context;
    const getColumn = () => {
        if (editFormColumn) {
            if (typeof editFormColumn === 'number') {
                return editFormColumn;
            }
            if (typeof editFormColumn === 'object' && editFormColumn !== null) {
                return editFormColumn[editAction === 'new' ? 'new' : 'edit'];
            }
        }
        return 2;
    }
    const getWidth = () => {
        if (editFormWidth) {
            if (typeof editFormWidth === 'number') {
                return editFormWidth;
            }
            if (typeof editFormWidth === 'object' && editFormWidth !== null) {
                return editFormWidth[editAction === 'new' ? 'new' : 'edit'];
            }
        }
        return 600;
    }
    if (!editRecord) return null;
    return (
        <GdxFormDialog
            open={editAction && true || false} onClose={() => {
                setEditAction(undefined, null);
            }} model={editRecord}
            title={editExtra && editExtra.title || editAction === 'new' && rl('New') || editAction === 'batch_edit' && rl('Batch Edit')
                || editAction === 'copy' && rl('Copy') || rl('Edit')}
            column={getColumn()}
            width={getWidth()}
            onSubmit={async (data) => {
                try {
                    let resp;
                    setErrorMsg('');
                    if (editExtra?.onSubmit) {
                        resp = await editExtra.onSubmit(data, context);
                    } else if (editAction === 'batch_edit') {
                        resp = await batchUpdate(data);
                    } else {
                        resp = await persist(data, editRecord._rowIndex);
                    }
                    if (resp.status === 0) {
                        // enqueueSnackbar(`${rl('Save success')}!`);
                        toast.success(`${rl('Save success')}!`)
                        reload();
                        setEditAction(undefined, null);
                    } else {
                        setErrorMsg(resp.message);
                    }
                } catch (error) {
                    setErrorMsg(typeof error === 'string' ? error : error.message);
                }
            }}
            validators={editFormValidators} {...editFormProps}>
            {!!errorMsg && <Alert severity="error">{errorMsg}</Alert>}
            {editForm}
        </GdxFormDialog>
    )
}