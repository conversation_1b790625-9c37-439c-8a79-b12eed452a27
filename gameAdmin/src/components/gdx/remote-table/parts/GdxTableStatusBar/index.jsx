import { useMemo, useState, useEffect } from "react";

import { Box, Tab, Tabs, alpha } from "@mui/material";

import GdxTag from "src/components/gdx/gdx-data/components/GdxTag";
import GdxTypeDataWrapper, { GdxTypeDataValueCount } from "src/components/gdx/gdx-data/components/GdxTypeDataWrapper";

import { useRemoteTableContext } from "../../context/hook";

const formatValue = (v) => v === undefined ? '_all' : `${v}`
export default function GdxTableStatusBar() {
    const { options: { statusFilter } } = useRemoteTableContext();
    if (!statusFilter) return null;
    return (
        <GdxTypeDataWrapper
            allFlag={statusFilter.showAll || false}
            type={statusFilter.dataKey} render={({ options }) => {
                if (!options || !options.length) return null;
                return (
                    <StatusTabs options={options}></StatusTabs>
                )
            }}></GdxTypeDataWrapper>

    )
}


function StatusTabs({ options }) {
    const { filter, updateFilter, options: { statusFilter, baseUrl } } = useRemoteTableContext();
    const [okay, setOkay] = useState(false);
    const fid = statusFilter.id;
    const curValue = useMemo(() => (formatValue(filter[fid])), [filter, statusFilter]);
    useEffect(() => {
        if (!curValue || !options || !options.length) return;
        const opt = options.find((x) => (formatValue(x.id) === curValue));
        if (!opt) {
            updateFilter({ ...filter, ...{ [fid]: formatValue(options[0].id) } });
        } else {
            setOkay(true);
        }
    }, [options, curValue])
    if (!okay) return null;
    return (
        <Tabs
            value={curValue}
            onChange={(e, newValue) => {
                let v = newValue;
                if (v === '_all') v = undefined;
                updateFilter({ ...filter, ...{ [fid]: v } });
            }}
            sx={{
                px: 2.5,
                boxShadow: (theme) => `inset 0 -2px 0 0 ${alpha(theme.palette.grey[500], 0.08)}`,
            }}
        >
            {options.map((opt) => (<Tab
                key={formatValue(opt.id)}
                iconPosition="end"
                value={formatValue(opt.id)}
                label={opt.name}
                icon={
                    statusFilter?.noCount ? undefined : <Box>
                        <GdxTypeDataValueCount
                            fetchCountUrl={`${baseUrl}/count`}
                            field={statusFilter.id}
                            value={opt.id} countQuery={statusFilter.countQuery}
                            render={({ count }) => (
                                count !== undefined && <GdxTag variant={curValue === formatValue(opt.id) && 'filled' || 'outlined'} color={opt.color}>{count}</GdxTag> || ''
                            )}></GdxTypeDataValueCount>
                    </Box>
                }
            />))}
        </Tabs>
    )
}