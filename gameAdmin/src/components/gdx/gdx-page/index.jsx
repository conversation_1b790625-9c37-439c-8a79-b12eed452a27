import { Button, Container } from '@mui/material';

import { RouterLink } from 'src/routes/components';

import { Iconify } from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';
import { useSpaSettingContext } from 'src/components/spa-setting-provider';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs/custom-breadcrumbs';

export default function GdxPage({ children, options }) {
  const settings = useSettingsContext();
  const { hideBreadcrumbs, inSpa } = useSpaSettingContext();
  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'} disableGutters={inSpa}>
      {!hideBreadcrumbs && (
        <CustomBreadcrumbs
          heading={options.title}
          links={options.breadcrumbs || []}
          action={
            options.newAction && (
              <Button
                component={RouterLink}
                variant="contained"
                startIcon={<Iconify icon="mingcute:add-line" />}
                {...options.newAction}
              />
            )
          }
          sx={{ mb: { xs: 3, md: 5, textTransform: 'capitalize' } }}
        />
      )}
      {children}
    </Container>
  );
}
