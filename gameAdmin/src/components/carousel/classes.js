// ----------------------------------------------------------------------

export const carouselClasses = {
  root: 'mnl__carousel__root',
  container: 'mnl__carousel__container',
  // dot
  dots: 'mnl__carousel__dots',
  dot: 'mnl__carousel__dot',
  // arrow
  arrows: 'mnl__carousel__arrows',
  arrowsLabel: 'mnl__carousel__arrows_label',
  arrowPrev: 'mnl__carousel__btn--prev',
  arrowNext: 'mnl__carousel__btn--next',
  arrowSvg: 'mnl__carousel__btn__svg',
  // slide
  slide: 'mnl__carousel__slide',
  slideContent: 'mnl__carousel__slide__content',
  // thumb
  thumbs: 'mnl__carousel__thumbs',
  thumb: 'mnl__carousel__thumb',
  thumbContainer: 'mnl__carousel__thumbs__container',
  thumbImage: 'mnl__carousel__thumb__image',
  // progress
  progress: 'mnl__carousel__progress',
  progressBar: 'mnl__carousel__progress__bar',
  state: { selected: 'state--selected', disabled: 'state--disabled' },
};
