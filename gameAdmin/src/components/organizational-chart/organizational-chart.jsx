import { cloneElement } from 'react';
import { Tree, TreeNode } from 'react-organizational-chart';

import { useTheme } from '@mui/material/styles';

import { flattenArray } from 'src/utils/helper';

// ----------------------------------------------------------------------

export function OrganizationalChart({ data, nodeItem, ...other }) {
  const theme = useTheme();

  const cloneNode = (props) => cloneElement(nodeItem(props));

  const label = cloneNode({
    ...data,
  });

  return (
    <Tree
      lineWidth="1.5px"
      nodePadding="4px"
      lineBorderRadius="24px"
      lineColor={theme.vars.palette.divider}
      label={label}
      {...other}
    >
      {data.children.map((list, index) => (
        <TreeList key={index} depth={1} data={list} nodeItem={nodeItem} />
      ))}
    </Tree>
  );
}

// ----------------------------------------------------------------------

export function TreeList({ data, depth, nodeItem }) {
  const childs = data.children;

  const cloneNode = (props) => cloneElement(nodeItem(props));

  const totalChildren = childs ? flattenArray(childs)?.length : 0;

  const label = cloneNode({
    ...data,
    depth,
    totalChildren,
  });

  return (
    <TreeNode label={label}>
      {childs && <TreeSubList data={childs} depth={depth} nodeItem={nodeItem} />}
    </TreeNode>
  );
}

// ----------------------------------------------------------------------

function TreeSubList({ data, depth, nodeItem }) {
  return (
    <>
      {data.map((list, index) => (
        <TreeList key={index} data={list} depth={depth + 1} nodeItem={nodeItem} />
      ))}
    </>
  );
}
