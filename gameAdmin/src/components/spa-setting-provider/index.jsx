import { useMemo, useContext, createContext } from 'react';

const SpaSettingsContext = createContext({
	hideBreadcrumbs: false,
	inSpa: false,
});

export function SpaSettingProvider({ children }) {

	const memoizedValue = useMemo(
		() => ({
			inSpa: true,
			hideBreadcrumbs: true,
		}),
		[]
	);

	return <SpaSettingsContext.Provider value={memoizedValue}>{children}</SpaSettingsContext.Provider>;
}

export function useSpaSettingContext() {
	const context = useContext(SpaSettingsContext);
	if (context === undefined) {
		throw new Error('useSpaSettings must be used within a SpaSettingProvider');
	}
	return context;
}
