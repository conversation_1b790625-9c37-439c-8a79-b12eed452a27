{"name": "@minimal-kit/vite-js", "author": "Minimals", "version": "6.0.1", "description": "Vite & JavaScript", "private": true, "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "rm:all": "rm -rf node_modules .next out dist build", "re:start": "yarn rm:all && yarn install && yarn dev", "re:build": "yarn rm:all && yarn install && yarn build", "re:build-npm": "npm run rm:all && npm install && npm run build", "dev:host": "vite --host"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/barlow": "^5.0.13", "@fontsource/dm-sans": "^5.0.21", "@fontsource/inter": "^5.0.18", "@fontsource/nunito-sans": "^5.0.13", "@fontsource/public-sans": "^5.0.18", "@fullcalendar/core": "^6.1.14", "@fullcalendar/daygrid": "^6.1.14", "@fullcalendar/interaction": "^6.1.14", "@fullcalendar/list": "^6.1.14", "@fullcalendar/react": "^6.1.14", "@fullcalendar/timegrid": "^6.1.14", "@fullcalendar/timeline": "^6.1.14", "@hookform/resolvers": "^3.6.0", "@iconify/react": "^5.0.1", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.20", "@mui/x-data-grid": "^7.7.0", "@mui/x-date-pickers": "^7.7.0", "@mui/x-date-pickers-pro": "^8.2.0", "@mui/x-tree-view": "^7.7.0", "@react-pdf/renderer": "^3.4.4", "@supabase/supabase-js": "^2.43.4", "@tiptap/core": "^2.4.0", "@tiptap/extension-code-block": "^2.4.0", "@tiptap/extension-code-block-lowlight": "^2.4.0", "@tiptap/extension-image": "^2.4.0", "@tiptap/extension-link": "^2.4.0", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-text-align": "^2.4.0", "@tiptap/pm": "^2.4.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@uiw/react-md-editor": "^4.0.6", "apexcharts": "^3.49.1", "autosuggest-highlight": "^3.3.4", "aws-amplify": "^6.3.6", "axios": "^1.7.2", "classnames": "^2.5.1", "dayjs": "^1.11.13", "embla-carousel": "^8.1.5", "embla-carousel-auto-height": "^8.1.5", "embla-carousel-auto-scroll": "^8.1.5", "embla-carousel-autoplay": "^8.1.5", "embla-carousel-react": "^8.1.5", "firebase": "^10.12.2", "framer-motion": "^11.2.10", "history": "^5.3.0", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-resources-to-backend": "^1.2.1", "lodash": "^4.17.21", "lowlight": "^3.1.0", "mapbox-gl": "^3.4.0", "md5": "^2.3.0", "mui-one-time-password-input": "^2.0.2", "nprogress": "^0.2.0", "numeral": "^2.0.6", "prop-types": "^15.8.1", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-email-editor": "^1.7.11", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.51.5", "react-i18next": "^14.1.2", "react-joyride": "^2.8.2", "react-lazy-load-image-component": "^1.6.0", "react-map-gl": "^7.1.7", "react-markdown": "^9.0.1", "react-organizational-chart": "^2.2.1", "react-phone-number-input": "^3.4.3", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "rehype-highlight": "^7.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "simplebar-react": "^3.2.5", "sonner": "^1.5.0", "stylis": "^4.3.2", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.2.5", "turndown": "^7.2.0", "yet-another-react-lightbox": "^3.20.0", "yup": "^1.6.1", "zod": "^3.23.8"}, "devDependencies": {"@types/react": "^18.3.3", "@vitejs/plugin-react-swc": "^3.7.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-perfectionist": "^2.11.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-unused-imports": "^3.2.0", "prettier": "^3.3.2", "sass": "^1.87.0", "typescript": "^5.4.5", "vite": "^5.3.0", "vite-plugin-checker": "^0.6.4"}}