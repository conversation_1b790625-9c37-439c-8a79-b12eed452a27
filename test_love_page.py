#!/usr/bin/env python3
"""
测试Love分页查询是否包含评论数量
"""
import asyncio
import sys
import os

# 添加项目路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'cloudCardBackend'))

from app.Admin.Love.services import LoveService
from app.Admin.Love.models import LovePageQuery
from app.Common.utils import ReturnObject

async def test_love_page_with_comment_count():
    """测试Love分页查询包含评论数量"""
    try:
        # 创建服务实例
        love_service = LoveService()
        
        # 创建查询参数
        query = LovePageQuery(
            pageNo=1,
            pageSize=10
        )
        
        # 模拟管理员信息
        admin_info = {
            "id": "test_admin_id",
            "username": "test_admin"
        }
        
        print("🔍 开始测试Love分页查询...")
        
        # 执行分页查询
        try:
            await love_service.page(query, admin_info)
        except ReturnObject as result:
            if hasattr(result, 'page') and result.page:
                items = result.page.get('content', [])
                print(f"✅ 查询成功，共找到 {len(items)} 条记录")
                
                # 检查每条记录是否包含commentCount字段
                for i, item in enumerate(items):
                    comment_count = item.get('commentCount', 'N/A')
                    title = item.get('title', 'N/A')
                    print(f"   记录 {i+1}: {title} - 评论数: {comment_count}")
                
                if items and all('commentCount' in item for item in items):
                    print("✅ 所有记录都包含commentCount字段")
                elif items:
                    print("⚠️  部分记录缺少commentCount字段")
                else:
                    print("ℹ️  没有找到记录")
                    
            else:
                print("❌ 查询结果格式异常")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_love_page_with_comment_count())
