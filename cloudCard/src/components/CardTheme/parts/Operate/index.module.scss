.operate {
    margin: 20rpx;
    border-radius: 20rpx;
    height: 100rpx;
    background: var(--app-card-background);
    display: flex;
    align-items: center;
    justify-content: space-around;

    .operateItem {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 5rpx;
        border-right: 1px solid var(--nutui-color-border);
        font-size: var(--nutui-font-text-small);
        color: var(--app-text-color);
        font-weight: bolder;

        &:last-child {
            border-right: none;
        }

        &:active {
            opacity: 0.5;
        }

        &.disableClick {
            opacity: unset;
        }

        &.collect {
            color: var(--nutui-orange-6);
        }

        &.like {
            color: var(--nutui-red-8);
        }

        .value {
            font-weight: 400;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10rpx;
            font-size: var(--nutui-font-text);

            .valueText {
                font-size: var(--nutui-font-text);
                line-height: var(--nutui-font-text);
            }
        }
    }
}