.root {
    .card {
        margin: 20rpx;
        border-radius: 20rpx;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .cardInfo {
            background: var(--app-primary-color);
            color: var(--app-text-color-primary);
            padding: 40rpx;

            .avatar_name {
                display: flex;
                align-items: center;
                gap: 20rpx;

                .avatar {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .name {
                    display: flex;
                    flex-direction: column;
                    gap: 10rpx;
                    justify-content: center;

                    .nameText {
                        font-size: var(--nutui-font-text-large);
                        font-weight: bolder;
                    }

                    .position {
                        opacity: 0.9;
                        font-size: var(--nutui-font-text-small);
                    }
                }
            }

            .company {
                margin: 30rpx 0;
                font-size: var(--nutui-font-text-large);
                font-weight: bolder;
            }

            .moreInfo {
                display: flex;
                flex-direction: column;
                gap: 15rpx;
                font-size: var(--nutui-font-text-small);
                opacity: 0.9;

                .row {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    &:active {
                        opacity: 0.7;
                    }

                    .left {
                        display: flex;
                        align-items: center;
                        gap: 20rpx;

                        .title {
                            font-size: var(--nutui-font-text-small);
                            line-height: var(--nutui-font-text-small);
                        }
                    }

                    .right {
                        display: flex;
                        align-items: center;
                        gap: 5rpx;
                        opacity: 0.7;

                        .extra {
                            font-size: var(--nutui-font-text-small);
                            line-height: var(--nutui-font-text-small);
                        }
                    }
                }
            }
        }

        .otherInfo {
            padding: 40rpx;
            background: var(--app-card-background);
            color: var(--app-text-color);
            display: flex;
            flex-direction: column;
            gap: 40rpx;

            .item {
                display: flex;
                gap: 20rpx;

                .describe {
                    flex: 1;
                    font-size: var(--nutui-font-text);
                    line-height: var(--nutui-font-text);
                }
            }
        }
    }
}