.dialogContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20rpx;

    .avatar {
        display: flex;
        padding: 0;
        border-radius: 200rpx;
        border: 1px solid var(--app-primary-color);
        align-items: center;
        justify-content: center;
    }

    .nickname {
        border: 1px solid var(--app-primary-color);
        border-radius: 200rpx;
        padding: 10rpx 20rpx;
    }
}

.userInfo {
    width: 100%;
    padding: 40rpx;
    display: flex;
    align-items: center;
    gap: 20rpx;

    .avatar{
        border: 1px solid var(--app-primary-color);
    }

    .name {
        // color: var(--app-primary-color);
        font-size: var(--nutui-font-text);
        font-weight: bolder;
        display: flex;
        align-items: center;
        gap: 10rpx;
    }
}