import { Button, Input, View } from "@tarojs/components";
import styles from "./index.module.scss";
import { useEffect, useState } from "react";
import utils from "../../common/utils";
import Taro from "@tarojs/taro";
import GbAvatar from "../GbAvatar";
import { ArrowSize6 } from "@nutui/icons-react-taro";
import { Dialog } from "@nutui/nutui-react-taro";

const GbUserLogin = () => {
	const [userInfo, setUserInfo] = useState(null);
	const [dialogVisible, setDialogVisible] = useState(false);

	const [nickname, setNickname] = useState(null);
	const [avatarUrl, setAvatarUrl] = useState(null);
	const [avatarVersion, setAvatarVersion] = useState(0);
	const getUserInfo = async () => {
		if (!utils.getToken()) return;
		const resp = await utils.request({
			api: "/api/user/info",
			showAlert: false,
		});
		if (resp.status !== 0) return resp;
		setUserInfo(resp.model);
		utils.setToken(resp.model.token);
		return resp;
	};
	useEffect(() => {
		getUserInfo();
	}, []);
	const handleClickUserBlock = async () => {
		if (utils.getToken()) {
			setDialogVisible(true);
			return;
		}
		Taro.showLoading({ title: "加载中...", mask: true });
		try {
			// 调用 wx.login 获取 code
			const { code } = await Taro.login();

			if (code) {
				// 将 code 发送到后端，获取用户的 session 信息
				const resp = await utils.request({
					api: "/api/user/login",
					data: { code },
				});
				if (resp.status !== 0) return;
				utils.setToken(resp.model.token);
				const user_resp = await getUserInfo();
				if (user_resp.status !== 0) {
					setDialogVisible(true);
				}
			}
		} catch (error) {
			Taro.showToast({
				title: "登录错误",
				icon: "none",
			});
		}
		Taro.hideLoading();
	};
	const onConfirm = async () => {
		if (!avatarUrl && !userInfo?.avatarUrl) {
			Taro.showToast({
				title: "请上传头像",
				icon: "none",
			});
			return;
		}
		if (!nickname && !userInfo?.nickname) {
			Taro.showToast({
				title: "请输入昵称",
				icon: "none",
			});
			return;
		}
		Taro.showLoading({
			title: userInfo?.token ? "修改中..." : "登录中...",
			mask: true,
		});
		let avatarUploadUrl = null;
		if (avatarUrl) {
			await Taro.uploadFile({
				url: `${YS_API_URL}/api/user/avatarUpload`,
				filePath: avatarUrl,
				name: "file",
				header: {
					"Content-Type": "multipart/form-data",
					token: utils.getToken(),
				},
				success: function (res) {
					avatarUploadUrl = res.data;
					setAvatarVersion(avatarVersion + 1);
				},
			});
		}
		const resp = await utils.request({
			api: "/api/user/save",
			data: {
				...(nickname && { nickname }),
				...(avatarUploadUrl && { avatarUrl: avatarUploadUrl }),
			},
		});
		Taro.hideLoading();
		if (resp.status !== 0) return;
		setUserInfo(resp.model);
		Taro.showToast({
			title: userInfo?.token ? "修改成功" : "登录成功",
			icon: "none",
		});
		utils.setToken(resp.model.token);
		setDialogVisible(false);
	};
	return (
		<>
			<Dialog
				title="头像昵称获取"
				visible={dialogVisible}
				closeOnOverlayClick={false}
				confirmText={userInfo?.token ? "修改" : "登录"}
				cancelText="取消"
				onConfirm={onConfirm}
				onCancel={() => {
					!userInfo?.token && utils.setToken(null);
					setDialogVisible(false);
				}}
			>
				<View className={styles.dialogContent}>
					<Button
						openType="chooseAvatar"
						onChooseAvatar={(e) => setAvatarUrl(e.detail.avatarUrl)}
						className={styles.avatar}
					>
						<GbAvatar
							src={
								avatarUrl ||
								(userInfo?.avatarUrl
									? YS_API_URL +
									  "/" +
									  userInfo?.avatarUrl +
									  "?v=" +
									  avatarVersion
									: null)
							}
						/>
					</Button>
					<Input
						type="nickname"
						defaultValue={nickname || userInfo?.nickname}
						onInput={(e) => setNickname(e.detail.value)}
						placeholder="请输入昵称"
						className={styles.nickname}
					/>
				</View>
			</Dialog>
			<View className={styles.userInfo} onClick={handleClickUserBlock}>
				<GbAvatar
                    className={styles.avatar}
					src={
						userInfo?.avatarUrl
							? YS_API_URL + "/" + userInfo?.avatarUrl + "?v=" + avatarVersion
							: null
					}
				/>
				<View className={styles.name}>
					{userInfo?.nickname || "微信登录授权"}
					<ArrowSize6 width="var(--nutui-font-text)" />
				</View>
			</View>
		</>
	);
};

export default GbUserLogin;
