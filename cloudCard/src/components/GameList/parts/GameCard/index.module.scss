.root {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: #fff;
  padding: 20rpx;
  border-radius: 20rpx;

  .img {
    border-radius: 10rpx;
    overflow: hidden;
  }

  .content {
    flex: 1;
    height: 128rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 3rpx;

    .title {
      font-size: var(--nutui-font-text);
      font-weight: bolder;
    }

    .desc {
      font-size: var(--nutui-font-text-small);
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
    }

    .other {
      font-size: var(--nutui-font-text-small);
      color: green;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .button {
        width: auto;
        height: 45rpx;
        border-radius: 10rpx;
      }
    }
  }
}