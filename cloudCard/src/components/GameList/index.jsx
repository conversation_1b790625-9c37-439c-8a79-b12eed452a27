import { View } from "@tarojs/components";
import styles from "./index.module.scss";
import { useEffect, useState } from "react";
import GameCard from "./parts/GameCard";
import utils from "../../common/utils";

const GameList = () => {
	const [list, setList] = useState([]);
	const [scrollTop, setScrollTop] = useState(0);
	console.log(scrollTop);
	useEffect(() => {
		// const dataList = [];
		// for (let i = 0; i < 100; i++) {
		// 	dataList.push(`Item ${i}`);
		// }
		// setList(dataList);
		fetchGameList();
	}, []);

	const fetchGameList = async () => {
		const resp = await utils.request({
			api: "/api/game/page",
			data: {
				offset: "",
			},
		});
		setList(resp?.model?.app_list || []);
	};

	const handleScroll = (e) => {
		setScrollTop(e.target.scrollTop);
	};

	const renderItem = (item, index) => {
		return (
			<View key={index} className={styles.item}>
				<GameCard item={item} />
			</View>
		);
	};
	return (
		<View className={styles.content} onScroll={handleScroll}>
			{list.map((item, index) => renderItem(item, index))}
		</View>
	);
};

export default GameList;
