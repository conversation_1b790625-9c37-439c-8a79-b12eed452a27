.root {
    width: 100%;
    position: relative;
    display: grid;
    grid-template-columns: repeat(var(--part-grid-columns), 1fr);
    gap: var(--part-grid-gap);

    .uploadWrapper,
    :global(.nut-image) {
        position: relative;
        width: 100%;
        height: 0 !important;
        padding-top: 100%;


        .upload,
        :global(.nut-img) {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            border-radius: var(--app-radius);
            overflow: visible;

            .delete {
                position: absolute;
                top: 0;
                right: 0;
                transform: translate(50%, -50%);
            }
        }
    }

    .uploadWrapper {
        .upload {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20rpx;
            color: var(--app-text-color);
            font-size: 20rpx;
            line-height: 20rpx;
            font-weight: bolder;
            border: 1px dashed var(--app-text-color);
        }
    }
}