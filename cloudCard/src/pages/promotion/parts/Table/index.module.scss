.root {
    padding: 20rpx;
    background: #fff;
    border-radius: 20rpx;

    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .left {
            display: flex;
            gap: 10rpx;
            align-items: center;

            .icon {
                color: #e67e22;
                font-size: var(--nutui-font-text-large);
            }

            .text {
                color: 000;
                font-size: var(--nutui-font-text-large);
                font-weight: bolder;
            }
        }

        .right {
            color: #333;
            font-size: var(--nutui-font-text-small);
        }
    }

    .total {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 40rpx;
        background: #4794e1;
        color: #fff;
        font-size: var(--nutui-font-text);
        font-weight: bolder;
        border-radius: 10rpx;
        overflow: hidden;
        margin-bottom: 10rpx;
    }

    :global {
        .nut-table-main-head {
            background: #97c3ee;
            color: #fff;
        }

        .nut-table-main-body {
            color: #333;
        }

        .nut-table-wrapper {
            border: none;
        }

        .nut-table-main {
            border-radius: 10rpx;
            overflow: hidden;
        }
    }
}