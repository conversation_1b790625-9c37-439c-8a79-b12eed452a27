.root {
  .balanceCard {
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    gap: 30rpx;

    .label {
      font-size: 28rpx;
      color: #666;
    }

    .amount {
      font-size: 60rpx;
      font-weight: bold;
      color: #000;
    }

    .stats {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 0;
      border-top: 1px solid #eee;
      border-bottom: 1px solid #eee;

      .statItem {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10rpx;

        .label {
          font-size: 24rpx;
          color: #999;
        }

        .value {
          font-size: 32rpx;
          color: #333;
        }
      }
    }

    .withdrawBtn {
      background: var(--nutui-color-primary);
      color: #fff;
      text-align: center;
      padding: 20rpx;
      border-radius: 10rpx;
      font-size: 32rpx;

      &:active {
        opacity: 0.8;
      }
    }
  }

  .incomeList {
    margin-top: 20rpx;

    .cellTitle {
      display: flex;
      align-items: center;
      gap: 10rpx;

      .icon {
        color: var(--nutui-color-primary);
      }
    }

    .income {
      color: #f5222d;
      font-weight: bold;
    }
  }
}
