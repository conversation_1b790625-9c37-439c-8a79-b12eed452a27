.gameHead {
    display: flex;
    gap: 20rpx;
    padding: 20rpx;

    .gameIcon {
        min-width: 240rpx;
        border-radius: 20rpx;
        overflow: hidden;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    }

    .gameInfo {
        display: flex;
        flex-direction: column;
        gap: 12rpx;
        flex: 1;

        .gameName {
            font-size: 36rpx;
            font-weight: bolder;
            color: var(--nutui-text-color);
        }

        .gameRating {
            display: flex;
            align-items: center;
            gap: 12rpx;

            .ratingText {
                font-size: 24rpx;
                color: var(--nutui-text-color-weak);
            }
        }

        .gameDesc {
            font-size: 24rpx;
            color: var(--nutui-text-color-weak);
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
            line-height: 1.4;
        }

        .gameTags {
            display: flex;
            flex-wrap: wrap;
            gap: 12rpx;
            
            :global(.nut-tag) {
                padding: 4rpx 12rpx;
                font-size: 22rpx;
            }
        }
    }
}

.gameGallery {
    margin-top: 20rpx;
    padding: 30rpx;

    .sectionTitle {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
        color: var(--nutui-text-color);
    }

    .imageList {
        width: auto;
        display: flex;
        overflow-x: scroll;
        gap: 20rpx;
        padding: 10rpx 0;
        margin-bottom: 20rpx;

        .image {
            min-width: 300rpx;
            border-radius: 20rpx;
            overflow: hidden;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;

            &:active {
                transform: scale(0.98);
            }
        }
    }
}

.gameFeatures {
    margin-top: 20rpx;
    margin-bottom: 100rpx;
    padding: 30rpx;

    .sectionTitle {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 30rpx;
        color: var(--nutui-text-color);
    }

    .featureList {
        display: flex;
        flex-direction: column;
        gap: 30rpx;

        .featureItem {
            display: flex;
            flex-direction: column;
            gap: 10rpx;

            .featureTitle {
                font-size: 30rpx;
                font-weight: bold;
                color: var(--nutui-text-color);
            }

            .featureDesc {
                font-size: 26rpx;
                color: var(--nutui-text-color-weak);
                line-height: 1.5;
            }
        }
    }
}

.actionBar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx 30rpx;
    background: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}