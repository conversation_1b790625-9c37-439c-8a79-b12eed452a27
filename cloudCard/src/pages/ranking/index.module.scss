.root {
    min-height: 100vh;
}

.header {
    background-color: #4b87ff;
    color: #fff;
    padding: 120rpx 0 120rpx 60rpx;
    position: relative;
}

.title {
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
}

.subtitle {
    font-size: 28rpx;
    opacity: 0.9;
}

.contentBox {
    margin-top: -100rpx;
    padding: 0 40rpx;
}

.tabWrapper {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding-top: 10rpx;
    overflow: hidden;
}

.tabsComponent {
    :global {
        .nut-tabs-titles {
            background-color: #fff !important;
            border-radius: 15rpx 15rpx 0 0 !important;
            overflow: hidden !important;
            font-size: 32rpx;
            height: 88rpx;
        }
        
        .nut-tabs__titles-item {
            color: #333;
            font-weight: normal;
            
            &.active {
                color: #333;
                font-weight: bold;
            }
        }
        
        .nut-tabs__content {
            background-color: transparent;
            padding: 0;
            display: none;
        }
        
        .nut-tabs__line {
            background-color: #4b87ff;
        }
    }
}

.rankingList {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 0 0 20rpx 20rpx;
    box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.05);
}

.rankingItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
        border-bottom: none;
    }
}

.rankLeft {
    display: flex;
    align-items: center;
}

.rankNum {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    font-size: 28rpx;
    color: #999;
}

.medal {
    width: 44rpx;
    height: 44rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-weight: bold;
}

.top1 {
    .medal {
        background-color: #ffd700;
    }
}

.top2 {
    .medal {
        background-color: #c0c0c0;
    }
}

.top3 {
    .medal {
        background-color: #cd7f32;
    }
}

.avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 20rpx;
    background-color: #f0f0f0;
}

.name {
    font-size: 30rpx;
    color: #333;
}

.amount {
    font-size: 32rpx;
    color: #4b87ff;
    font-weight: 500;
}
