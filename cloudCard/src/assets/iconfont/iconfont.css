@font-face {
  font-family: "iconfont";
  /* Project id 4652191 */
  src: url('iconfont.ttf?t=1725362104739') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-close_2:before {
  content: "\e61a";
}

.icon-close:before {
  content: "\e607";
}

.icon-edit:before {
  content: "\e604";
}

.icon-default-avatar:before {
  content: "\e678";
}

.icon-image:before {
  content: "\e63d";
}

.icon-images:before {
  content: "\e6fc";
}

.icon-qrcode:before {
  content: "\eaf1";
}

.icon-empty:before {
  content: "\e603";
}

.icon-love-fill:before {
  content: "\e601";
}

.icon-love:before {
  content: "\e602";
}

.icon-eye:before {
  content: "\e640";
}

.icon-star:before {
  content: "\e8b9";
}

.icon-star-fill:before {
  content: "\e8c6";
}

.icon-describe:before {
  content: "\ea84";
}

.icon-weixin:before {
  content: "\e600";
}

.icon-location-fill:before {
  content: "\e83d";
}

.icon-email-fill:before {
  content: "\e856";
}

.icon-phone-fill:before {
  content: "\e85f";
}

.icon-arrow-right:before {
  content: "\e63c";
}