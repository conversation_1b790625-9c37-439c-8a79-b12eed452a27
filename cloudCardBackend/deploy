#!/bin/bash

SERVER="root@182.92.133.199"
OUTPUT_DIR="./"
DEST_DIR="/www/wwwroot/gameServe"

ssh "$SERVER" "rm -rf $DEST_DIR"

if [ $? -ne 0 ]; then
  echo "rm -rf $DEST_DIR failed"
  exit 1
fi

rsync -av \
  --exclude='__pycache__' \
  --exclude='cloudCardVenv' \
  --exclude='.history' \
  --exclude='.git' \
  "$OUTPUT_DIR/" "$SERVER:$DEST_DIR"


if [ $? -ne 0 ]; then
  echo "upload failed"
  exit 1
fi