
# cloudCardBackend

cloudCardBackend 是一个基于 Python、FastAPI 和 MongoDB 的云名片管理系统。该项目提供了一个 RESTful API，用于管理和存储名片信息。

## 技术栈

- **Python**: 编程语言
- **FastAPI**: Web 框架，用于构建快速、高性能的 API
- **MongoDB**: NoSQL 数据库，用于存储名片信息

## 本地开发

以下是本地开发环境的设置步骤。

### 1. 克隆仓库

```bash
git clone https://github.com/your_username/cloudCardBackend.git
cd cloudCardBackend
```

### 2. 创建虚拟环境

使用 Python 3 创建虚拟环境并激活它：

```bash
python3 -m venv cloudCardVenv
source cloudCardVenv/bin/activate
```

### 3. 安装依赖

使用 `pip` 安装项目所需的依赖包：

```bash
pip install -r requirements.txt
```

### 4. 配置环境变量

根据项目需求，在项目根目录下创建 `.env` 文件，并配置相关的环境变量。例如：

```env
MONGO_URI=*********************************************************
```

### 5. 启动开发服务器

使用 FastAPI 运行项目：

```bash
python3 -m app.main
```

开发服务器将在 `http://127.0.0.1:8000` 上运行，你可以通过浏览器或 Postman 等工具访问该 API。

## 目录结构

```plaintext
cloudCardBackend/
│
├── app/
│   ├── main.py         # 项目入口文件
│   ├── models/         # 数据模型
│   ├── routes/         # API 路由
│   └── utils/          # 工具函数
│
├── cloudCardVenv/      # 虚拟环境
├── requirements.txt    # 项目依赖
└── README.md           # 项目文档
```

## 部署

如果需要部署到生产环境，请参考相关的部署文档。