from typing import Optional, List, Any, Dict, Generic, TypeVar
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


# 定义排序方向枚举
class SortDirection(int, Enum):
    ASC = 1  # 升序
    DESC = -1  # 降序


# 排序字段模型
class SortField(BaseModel):
    field: str = Field(..., description="排序字段名")
    sort: SortDirection = Field(
        SortDirection.DESC, description="排序方向: 1升序, -1降序"
    )


# 基础分页查询模型
class PageQuery(BaseModel):
    pageNo: int = Field(1, description="页码，从1开始")
    pageSize: int = Field(10, description="每页数量")
    sortField: Optional[str] = Field(None, description="排序字段")
    sortOrder: SortDirection = Field(
        SortDirection.DESC, description="排序方向: 1升序, -1降序"
    )
    sortFields: Optional[List[SortField]] = Field(
        None, description="排序字段列表，优先级高于sortField"
    )
    startTime: Optional[datetime] = Field(None, description="开始时间")
    endTime: Optional[datetime] = Field(None, description="结束时间")
    keyword: Optional[str] = Field(None, description="关键词搜索")


# 基础ID模型
class IdModel(BaseModel):
    id: str = Field(..., description="记录ID")


# 基础实体模型
class BaseEntity(BaseModel):
    id: Optional[str] = Field(None, description="记录ID")
    createdAt: Optional[int] = Field(None, description="创建时间")
    createdById: Optional[str] = Field(None, description="创建人ID")
    createdByName: Optional[str] = Field(None, description="创建人名称")
    updatedAt: Optional[int] = Field(None, description="更新时间")
    updatedById: Optional[str] = Field(None, description="更新人ID")
    updatedByName: Optional[str] = Field(None, description="更新人名称")
    deleted: bool = Field(False, description="是否删除")
    deletedAt: Optional[int] = Field(None, description="删除时间")
    deletedById: Optional[str] = Field(None, description="删除人ID")
    deletedByName: Optional[str] = Field(None, description="删除人名称")
    remark: Optional[str] = Field(None, description="备注")


# 批量操作模型
class BatchIdsModel(BaseModel):
    ids: List[str] = Field(..., description="记录ID列表")


# 批量更新模型
class BatchUpdateModel(BaseModel):
    ids: List[str] = Field(..., description="记录ID列表")
    update: Dict[str, Any] = Field(..., description="更新的字段")


# 分页结果模型
class PageResult(BaseModel):
    content: List[Any] = Field([], description="数据列表")
    number: int = Field(1, description="当前页码")
    size: int = Field(10, description="每页大小")
    totalElements: int = Field(0, description="总记录数")
    totalPages: int = Field(0, description="总页数")
    numberOfElements: int = Field(0, description="当前页记录数")


# 通用返回结果模型
class ResultModel(BaseModel):
    status: int = Field(0, description="状态码: 0成功, 其他失败")
    message: str = Field("操作成功", description="提示信息")
    model: Any = Field(None, description="返回数据")


# 通用时间范围模型
class timeRange(BaseModel):
    start: Optional[int] = Field(None, description="开始时间")
    finish: Optional[int] = Field(None, description="结束时间")
