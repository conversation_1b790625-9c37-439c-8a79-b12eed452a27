from typing import Type, Generic, TypeVar, Dict, Any, Optional
from fastapi import (
    API<PERSON><PERSON><PERSON>,
    Header,
    Path,
    Depends,
    HTTPException,
    Body,
    Query,
    Request,
)
from pydantic import BaseModel

from app.Common.base_models import (
    PageQuery,
    BaseEntity,
    IdModel,
    BatchIdsModel,
    BatchUpdateModel,
)
from app.Common.base_service import BaseService
from app.Common.utils import ReturnObject
from app.Admin.Account.services import AccountService


# 定义一个包装查询参数的模型
class QueryWrapper(BaseModel):
    query: Optional[Dict[str, Any]] = None


account_service = AccountService()


# 通用控制器基类
class BaseController:
    """
    通用CRUD控制器基类
    """

    def __init__(
        self,
        router: APIRouter,
        service: BaseService,
        entity_class: Type[BaseEntity],
        page_query_class: Type[PageQuery] = PageQuery,
        prefix: str = "",
        tags: list = None,
    ):
        """
        初始化控制器

        Args:
            router: FastAPI路由器
            service: 服务实例
            entity_class: 实体类
            page_query_class: 分页查询参数类
            prefix: 路由前缀
            tags: API标签
        """
        self.router = router
        self.service = service
        self.entity_class = entity_class
        self.page_query_class = page_query_class
        self.prefix = prefix
        self.tags = tags or [prefix.strip("/")]

        # 注册路由
        self.register_routes()

    def register_routes(self):
        """注册所有路由"""

        # 分页查询
        @self.router.post(f"{self.prefix}/page", tags=self.tags)
        @self.router.post(f"{self.prefix}/list", tags=self.tags)
        async def query_handler(
            request: Request,
            data: QueryWrapper = Body(...),
            limit: Optional[int] = Query(None),
            authorization: str = Header(None),
        ):
            try:
                # 强制使用管理员认证
                admin_info = await getAdminInfo(authorization)

                # 处理包含query字段的请求
                if data.query is not None:
                    # 使用query字段的内容创建PageQuery对象
                    query = self.page_query_class(**data.query)
                else:
                    # 如果没有query字段，使用默认值
                    query = self.page_query_class()

                # 根据路由判断是分页还是列表
                path = request.url.path
                is_list = path.endswith("/list")

                if is_list:
                    # 设置 pageSize 为 limit 参数（默认 100）
                    query.pageSize = limit or 100
                    await self.service.page(query, admin_info, is_list=True)
                else:
                    await self.service.page(query, admin_info)
            except ReturnObject as e:
                return e
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # 根据ID查询
        @self.router.post(f"{self.prefix}/one", tags=self.tags)
        async def get_by_id(data: IdModel, authorization: str = Header(None)):
            try:
                # 强制使用管理员认证
                admin_info = await getAdminInfo(authorization)
                await self.service.get_by_id(data.id, admin_info)
            except ReturnObject as e:
                return e
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # 批量删除记录
        @self.router.post(f"{self.prefix}/batch_delete", tags=self.tags)
        async def batch_delete(data: BatchIdsModel, authorization: str = Header(None)):
            try:
                # 强制使用管理员认证
                admin_info = await getAdminInfo(authorization)
                await self.service.batch_delete(data, admin_info)
            except ReturnObject as e:
                return e
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # 批量更新记录
        @self.router.post(f"{self.prefix}/batch_update", tags=self.tags)
        async def batch_update(
            data: BatchUpdateModel, authorization: str = Header(None)
        ):
            try:
                # 强制使用管理员认证
                admin_info = await getAdminInfo(authorization)
                await self.service.batch_update(data, admin_info)
            except ReturnObject as e:
                return e
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # POST 方式删除记录
        @self.router.post(f"{self.prefix}/delete", tags=self.tags)
        async def post_delete(data: IdModel, authorization: str = Header(None)):
            try:
                # 强制使用管理员认证
                admin_info = await getAdminInfo(authorization)
                await self.service.delete(data.id, admin_info)
            except ReturnObject as e:
                return e
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # 定义一个包装实体的模型
        class ModelWrapper(BaseModel):
            model: Optional[Dict[str, Any]] = None

        # 保存记录（创建或更新）
        @self.router.post(f"{self.prefix}/persist", tags=self.tags)
        async def persist_entity(
            data: ModelWrapper = Body(...), authorization: str = Header(None)
        ):
            try:
                # 强制使用管理员认证
                admin_info = await getAdminInfo(authorization)

                # 处理包含model字段的请求
                if data.model is not None:
                    # 使用model字段的内容创建实体对象
                    entity = self.entity_class(**data.model)
                else:
                    # 如果没有model字段，尝试直接使用data创建实体
                    entity = self.entity_class(**data.model_dump(exclude={"model"}))

                await self.service.persist(entity, admin_info)
            except ReturnObject as e:
                return e
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))


# 获取管理员信息
async def getAdminInfo(authorization: str):
    """
    获取管理员信息

    Args:
        authorization: Authorization header的值

    Returns:
        管理员信息

    Raises:
        ReturnObject: 当管理员未登录或不存在时
    """
    if not authorization:
        raise ReturnObject(status=401, message="请先登录")

    try:
        await account_service.account_info_model(authorization)
    except ReturnObject as e:
        if e.status != 0:
            raise ReturnObject(status=401, message="请先登录")
        else:
            return e.model
