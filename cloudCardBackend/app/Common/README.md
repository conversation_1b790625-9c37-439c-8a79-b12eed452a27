# 通用增删改查接口

本文档介绍了如何使用通用增删改查接口，快速构建后端API。

## 目录结构

```
app/Common/
  ├── base_models.py    # 基础数据模型
  ├── base_service.py   # 基础服务层
  └── base_controller.py # 基础控制器层
```

## 快速开始

### 1. 定义实体模型

首先，需要定义实体模型，继承自 `BaseEntity`：

```python
from pydantic import Field
from app.Common.base_models import BaseEntity

class YourEntity(BaseEntity):
    name: str = Field(..., description="名称")
    code: str = Field(..., description="编码")
    # 其他字段...
```

### 2. 定义查询参数（可选）

如果需要自定义查询参数，可以继承 `PageQuery`：

```python
from typing import Optional
from pydantic import Field
from app.Common.base_models import PageQuery

class YourPageQuery(PageQuery):
    name: Optional[str] = Field(None, description="名称")
    code: Optional[str] = Field(None, description="编码")
    # 其他查询参数...
```

### 3. 创建服务

创建服务类，继承自 `BaseService`：

```python
from app.Common.base_service import BaseService
from app.Common.db import db
from .models import YourEntity

# 创建集合
your_collection = db["your_collection"]

class YourService(BaseService):
    def __init__(self):
        super().__init__(your_collection, YourEntity)
    
    # 可以重写基类方法，添加自定义逻辑
    async def page(self, query, token=None):
        # 自定义查询逻辑...
        pass
```

### 4. 创建控制器

创建控制器，使用 `BaseController`：

```python
from fastapi import APIRouter
from app.Common.base_controller import BaseController
from .models import YourEntity, YourPageQuery
from .services import YourService

# 创建路由器
yourRouter = APIRouter()

# 创建服务实例
your_service = YourService()

# 创建控制器
your_controller = BaseController(
    router=yourRouter,
    service=your_service,
    entity_class=YourEntity,
    page_query_class=YourPageQuery,
    prefix="/your_entity",
    tags=["你的实体管理"],
    require_auth=True,
    admin_auth=True  # 是否需要管理员权限
)
```

### 5. 注册路由

在 `app/routes.py` 中注册路由：

```python
from fastapi import FastAPI
from .YourModule.controllers import yourRouter

app = FastAPI()

# 注册路由
app.include_router(yourRouter, prefix="/api/your_module")
```

## API 接口说明

使用 `BaseController` 会自动创建以下接口：

| 方法   | 路径                   | 描述         |
|------|----------------------|------------|
| POST | /your_entity/page    | 分页查询       |
| GET  | /your_entity/{id}    | 根据ID查询详情   |
| POST | /your_entity         | 创建记录       |
| PUT  | /your_entity/{id}    | 更新记录       |
| DELETE | /your_entity/{id}    | 删除记录       |
| POST | /your_entity/batch_delete | 批量删除记录     |
| POST | /your_entity/batch_update | 批量更新记录     |
| POST | /your_entity/persist | 保存记录(创建或更新) |

## 请求和响应格式

### 分页查询请求

```json
{
  "pageNo": 1,
  "pageSize": 10,
  "sortField": "createdAt",
  "sortOrder": -1,
  "startTime": "2023-01-01T00:00:00Z",
  "endTime": "2023-12-31T23:59:59Z",
  "keyword": "搜索关键词",
  // 其他自定义查询参数...
}
```

### 分页查询响应

```json
{
  "status": 0,
  "message": "操作成功",
  "model": {
    "page": {
      "content": [
        {
          "id": "60d5ec9f8f9f7a001b9e0f1a",
          "name": "示例名称",
          "code": "EXAMPLE_CODE",
          // 其他字段...
          "createdAt": 1624548511000,
          "updatedAt": 1624548511000
        }
        // 更多记录...
      ],
      "number": 1,
      "size": 10,
      "totalElements": 100,
      "totalPages": 10,
      "numberOfElements": 10
    }
  }
}
```

### 创建/更新请求

```json
{
  "name": "示例名称",
  "code": "EXAMPLE_CODE",
  // 其他字段...
}
```

### 创建/更新响应

```json
{
  "status": 0,
  "message": "操作成功",
  "model": {
    "id": "60d5ec9f8f9f7a001b9e0f1a",
    "name": "示例名称",
    "code": "EXAMPLE_CODE",
    // 其他字段...
    "createdAt": 1624548511000,
    "updatedAt": 1624548511000
  }
}
```

### 批量删除请求

```json
{
  "ids": ["60d5ec9f8f9f7a001b9e0f1a", "60d5ec9f8f9f7a001b9e0f1b"]
}
```

### 批量更新请求

```json
{
  "ids": ["60d5ec9f8f9f7a001b9e0f1a", "60d5ec9f8f9f7a001b9e0f1b"],
  "update": {
    "status": 0,
    "remark": "批量更新备注"
  }
}
```

## 自定义服务逻辑

如果需要添加自定义业务逻辑，可以在服务类中重写基类方法或添加新方法：

```python
class YourService(BaseService):
    def __init__(self):
        super().__init__(your_collection, YourEntity)
    
    async def page(self, query, token=None):
        # 自定义分页查询逻辑
        # ...
    
    async def custom_method(self, data, token=None):
        # 自定义业务方法
        # ...
        raise ReturnObject(model=result)
```

然后在控制器中添加自定义路由：

```python
# 在创建控制器后添加自定义路由
@yourRouter.post("/your_entity/custom", tags=["你的实体管理"])
async def custom_endpoint(data: YourCustomModel, token: str = Header(None)):
    try:
        await hasAdminLogin(token)
        await your_service.custom_method(data, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```
