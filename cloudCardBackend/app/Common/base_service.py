from typing import Dict, Type, Generic, TypeVar
from bson import ObjectId
from pymongo import ReturnDocument
from pymongo.collection import Collection

from app.Common.base_models import (
    PageQuery,
    BaseEntity,
    BatchIdsModel,
    BatchUpdateModel,
    timeRange,
)
from app.Common.utils import ReturnObject, getMillisecond


# 通用服务基类
class BaseService:
    """
    通用CRUD服务基类
    """

    def __init__(self, collection: Collection, entity_class: Type[BaseEntity]):
        """
        初始化服务

        Args:
            collection: MongoDB集合
            entity_class: 实体类
        """
        self.collection = collection
        self.entity_class = entity_class

    async def page(
        self, query: PageQuery, admin_info: dict = None, is_list: bool = False
    ) -> Dict:
        """
        分页查询

        Args:
            query: 分页查询参数
            admin_info: 管理员信息
            is_list: 是否是列表查询

        Returns:
            分页结果
        """
        # 构建查询条件
        filter_query = {"deleted": False}  # 添加软删除条件

        # 添加时间范围查询
        if query.startTime and query.endTime:
            filter_query["createdAt"] = {
                "$gte": int(query.startTime.timestamp() * 1000),
                "$lte": int(query.endTime.timestamp() * 1000),
            }

        # 计算分页参数
        skip = (query.pageNo - 1) * query.pageSize

        # 构建排序
        cursor = self.collection.find(filter_query)

        # 使用 sortFields 或 sortField 进行排序
        if query.sortFields and len(query.sortFields) > 0:
            # 使用多字段排序
            for sort_field_obj in query.sortFields:
                cursor = cursor.sort(sort_field_obj.field, sort_field_obj.sort)
        else:
            # 使用单字段排序（向后兼容）
            sort_field = query.sortField or "createdAt"
            sort_order = query.sortOrder
            cursor = cursor.sort(sort_field, sort_order)

        # 判断字段类型进行查询
        for name, field in query.model_fields.items():
            value = getattr(query, name)
            if value is None:
                continue

            meta = dict(field.json_schema_extra) if field.json_schema_extra else {}
            qtype = meta.get("query_type")

            if qtype == "exact":
                filter_query[name] = value

            elif qtype == "id":
                filter_query[name] = ObjectId(value)

            elif qtype == "like":
                filter_query[name] = {"$regex": value, "$options": "i"}

            elif qtype == "in":
                if isinstance(value, str):
                    filter_query[name] = {"$in": value.split(",")}
                elif isinstance(value, list):
                    filter_query[name] = {"$in": value}

            elif qtype in ("gte", "lte"):
                # createdTimeStart => createdTime
                field_name = name
                if name.endswith("Start"):
                    field_name = name[:-5]
                    filter_query.setdefault(field_name, {})["$gte"] = value
                elif name.endswith("End"):
                    field_name = name[:-3]
                    filter_query.setdefault(field_name, {})["$lte"] = value

            elif qtype == "date_range":
                if isinstance(value, timeRange):
                    range_query = {}
                    if value.start is not None:
                        range_query["$gte"] = value.start
                    if value.finish is not None:
                        range_query["$lte"] = value.finish
                    if range_query:
                        filter_query[name] = range_query

        # 查询总数
        total = await self.collection.count_documents(filter_query)

        # 分页查询
        cursor = cursor.skip(skip).limit(query.pageSize)
        items = await cursor.to_list(length=query.pageSize)

        # 构建分页结果
        page_result = {
            "content": items,
            "number": query.pageNo,
            "size": query.pageSize,
            "totalElements": total,
            "totalPages": (
                (total + query.pageSize - 1) // query.pageSize
                if query.pageSize > 0
                else 0
            ),
            "numberOfElements": len(items),
        }

        if is_list:
            raise ReturnObject(data_list=items)

        # 返回结果
        raise ReturnObject(page=page_result)

    async def get_by_id(self, id: str, admin_info: dict = None) -> Dict:
        """
        根据ID查询

        Args:
            id: 记录ID
            admin_info: 管理员信息

        Returns:
            记录详情
        """
        try:
            # 验证ObjectId格式
            object_id = ObjectId(id)
        except:
            raise ReturnObject(status=-1, message="ID格式错误")

        # 查询记录
        result = await self.collection.find_one({"_id": object_id, "deleted": False})

        if not result:
            raise ReturnObject(status=404, message="记录不存在")

        # 返回结果
        raise ReturnObject(model=result)

    async def create(self, data: BaseEntity, admin_info: dict = None) -> Dict:
        """
        创建记录

        Args:
            data: 创建的数据
            admin_info: 管理员信息

        Returns:
            创建的记录
        """
        # 转换为字典，包含所有字段（包括默认值）
        entity_dict = data.model_dump(exclude_unset=False)

        # 添加创建时间和更新时间
        current_time = getMillisecond()
        entity_dict["createdAt"] = current_time
        entity_dict["updatedAt"] = current_time

        # 验证token并获取管理员信息
        if admin_info:
            try:
                entity_dict["createdById"] = admin_info["id"]
                entity_dict["createdByName"] = admin_info["username"]
            except ReturnObject as e:
                # 如果token验证失败，记录错误但继续执行
                print(f"Token验证失败: {e.message}")

        # 插入数据
        result = await self.collection.insert_one(entity_dict)

        if result.inserted_id:
            # 查询插入的数据
            inserted_data = await self.collection.find_one({"_id": result.inserted_id})
            raise ReturnObject(model=inserted_data)

        raise ReturnObject(status=-1, message="创建失败")

    async def update(self, id: str, data: BaseEntity, admin_info: dict = None) -> Dict:
        """
        更新记录

        Args:
            id: 记录ID
            data: 更新的数据
            admin_info: 管理员信息

        Returns:
            更新后的记录
        """
        try:
            # 验证ObjectId格式
            object_id = ObjectId(id)
        except:
            raise ReturnObject(status=-1, message="ID格式错误")

        # 查询记录是否存在
        existing = await self.collection.find_one({"_id": object_id, "deleted": False})
        if not existing:
            raise ReturnObject(status=404, message="记录不存在")

        # 转换为字典并排除id字段
        update_data = data.model_dump(
            exclude={"id"}, exclude_unset=True, exclude_none=True
        )

        # 添加更新时间
        current_time = getMillisecond()
        update_data["updatedAt"] = current_time

        # 验证token并获取管理员信息
        if admin_info:
            try:
                update_data["updatedById"] = admin_info["id"]
                update_data["updatedByName"] = admin_info["username"]
            except ReturnObject as e:
                # 如果token验证失败，记录错误但继续执行
                print(f"Token验证失败: {e.message}")

        # 更新数据
        result = await self.collection.find_one_and_update(
            {"_id": object_id},
            {"$set": update_data},
            return_document=ReturnDocument.AFTER,
        )

        if result:
            raise ReturnObject(model=result)

        raise ReturnObject(status=-1, message="更新失败")

    async def delete(self, id: str, admin_info: dict = None) -> Dict:
        """
        删除记录（软删除）

        Args:
            id: 记录ID
            admin_info: 管理员信息

        Returns:
            删除结果
        """
        try:
            # 验证ObjectId格式
            object_id = ObjectId(id)
        except:
            raise ReturnObject(status=-1, message="ID格式错误")

        # 查询记录是否存在
        existing = await self.collection.find_one({"_id": object_id, "deleted": False})
        if not existing:
            raise ReturnObject(status=404, message="记录不存在")

        # 软删除
        current_time = getMillisecond()
        update_data = {"deleted": True, "deletedAt": current_time}

        # 验证token并获取管理员信息
        if admin_info:
            try:
                update_data["deletedById"] = admin_info["id"]
                update_data["deletedByName"] = admin_info["username"]
            except ReturnObject as e:
                # 如果token验证失败，记录错误但继续执行
                print(f"Token验证失败: {e.message}")

        # 更新数据
        result = await self.collection.update_one(
            {"_id": object_id}, {"$set": update_data}
        )

        if result.modified_count:
            raise ReturnObject(status=0, message="删除成功")

        raise ReturnObject(status=-1, message="删除失败")

    async def batch_delete(self, data: BatchIdsModel, admin_info: dict = None) -> Dict:
        """
        批量删除记录（软删除）

        Args:
            data: 批量删除参数
            admin_info: 管理员信息

        Returns:
            删除结果
        """
        if not data.ids:
            raise ReturnObject(status=-1, message="ID列表不能为空")

        try:
            # 转换为ObjectId列表
            object_ids = [ObjectId(id) for id in data.ids]
        except:
            raise ReturnObject(status=-1, message="ID格式错误")

        # 软删除
        current_time = getMillisecond()
        update_data = {"deleted": True, "deletedAt": current_time}

        # 验证token并获取管理员信息
        if admin_info:
            try:
                update_data["deletedById"] = admin_info["id"]
                update_data["deletedByName"] = admin_info["username"]
            except ReturnObject as e:
                # 如果token验证失败，记录错误但继续执行
                print(f"Token验证失败: {e.message}")

        # 更新数据
        result = await self.collection.update_many(
            {"_id": {"$in": object_ids}, "deleted": False}, {"$set": update_data}
        )

        if result.modified_count:
            raise ReturnObject(
                status=0, message=f"成功删除{result.modified_count}条记录"
            )

        raise ReturnObject(status=-1, message="删除失败")

    async def batch_update(
        self, data: BatchUpdateModel, admin_info: dict = None
    ) -> Dict:
        """
        批量更新记录

        Args:
            data: 批量更新参数
            admin_info: 管理员信息

        Returns:
            更新结果
        """
        if not data.ids:
            raise ReturnObject(status=-1, message="ID列表不能为空")

        if not data.update:
            raise ReturnObject(status=-1, message="更新内容不能为空")

        try:
            # 转换为ObjectId列表
            object_ids = [ObjectId(id) for id in data.ids]
        except:
            raise ReturnObject(status=-1, message="ID格式错误")

        # 添加更新时间
        current_time = getMillisecond()
        update_data = {**data.update, "updatedAt": current_time}

        # 验证token并获取管理员信息
        if admin_info:
            try:
                update_data["updatedById"] = admin_info["id"]
                update_data["updatedByName"] = admin_info["username"]
            except ReturnObject as e:
                # 如果token验证失败，记录错误但继续执行
                print(f"Token验证失败: {e.message}")

        # 更新数据
        result = await self.collection.update_many(
            {"_id": {"$in": object_ids}, "deleted": False}, {"$set": update_data}
        )

        if result.modified_count:
            raise ReturnObject(
                status=0, message=f"成功更新{result.modified_count}条记录"
            )

        raise ReturnObject(status=-1, message="更新失败")

    async def persist(self, data: BaseEntity, admin_info: dict = None) -> Dict:
        """
        保存记录（创建或更新）

        Args:
            data: 保存的数据
            admin_info: 管理员信息

        Returns:
            保存的记录
        """
        if data.id:
            return await self.update(data.id, data, admin_info)
        else:
            return await self.create(data, admin_info)
