from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from app.Modules.Card.controllers import cardRouter
from app.Modules.User.controllers import userRouter
from app.Modules.File.controllers import fileRouter, resourceRouter
from app.Modules.Game.controllers import gameRouter
from app.Modules.Carousel.controllers import carouselRouter
from app.Admin.Account.controllers import accountAdminRouter
from app.Admin.User.controllers import userAdminRouter
from app.Admin.Love.controllers import loveRouter
from app.Admin.LoveComment.controllers import loveCommentRouter

app = FastAPI(docs_url="/docs", redoc_url=None)


# 静态资源
app.mount("/assets", StaticFiles(directory="assets"), name="static")

# 允许跨域请求的来源
origins = [
    "https://admin.xxxxt.com",  # 部署后的前端地址
    "https://admin.yinshiwl.com",  # 部署后的前端地址
]

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # 允许的源
    allow_credentials=True,  # 是否允许携带 cookie
    allow_methods=["*"],  # 允许的请求方式 GET, POST 等
    allow_headers=["*"],  # 允许的请求头
)

# app
app.include_router(userRouter, prefix="/api/user")
app.include_router(cardRouter, prefix="/api/card")
app.include_router(fileRouter, prefix="/api/file")
app.include_router(gameRouter, prefix="/api/game")
app.include_router(carouselRouter, prefix="/api/carousel")


# admin
app.include_router(accountAdminRouter, prefix="/api/admin/account")
app.include_router(userAdminRouter, prefix="/api/admin/user")


# love
app.include_router(loveRouter, prefix="/api/love")
app.include_router(loveCommentRouter, prefix="/api/love/comment")

# resources
app.include_router(resourceRouter, prefix="/api/resources")
