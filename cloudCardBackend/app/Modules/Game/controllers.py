from fastapi import APIRouter, HTTPException, Header


from app.Modules.Game.models import GamePageQuery
from app.Modules.Game.services import game_page_model
from app.Common.utils import ReturnObject, hasLogin


gameRouter = APIRouter()


@gameRouter.post("/page")
async def game_page(query: GamePageQuery, token: str = Header(None)):
    try:
        await hasLogin(token)
        await game_page_model(query)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
