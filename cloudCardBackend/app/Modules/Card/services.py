import time

from bson import ObjectId
from pymongo import ReturnDocument
from app.Common.db import card, cardLike, cardCollect, cardBrowse
from app.Modules.Card.models import Card, Page, CardLike, CardCollect, CardBrowse

from app.Common.utils import (
    ReturnObject,
    findCardCreatedToken,
    formatModels,
    getMillisecond,
)


# 新增或更新名片数据
async def card_save_model(data: Card, token: str):
    save_data = data.model_dump(exclude_unset=True)
    current_time = getMillisecond()

    # 确定查询条件，考虑到可能的插入情况
    query = {"token": token}

    if data.id:
        # 更新名片
        query["_id"] = ObjectId(data.id)
        save_data["updatedTime"] = current_time
    else:
        # 新增名片
        query["createdTime"] = current_time
        save_data["createdTime"] = current_time
        save_data["deleted"] = False

    if data.deleted:
        save_data["deletedTime"] = current_time

    # 保存或更新名片记录
    result = await card.find_one_and_update(
        query,
        {"$set": save_data},
        upsert=True,
        return_document=ReturnDocument.AFTER,
    )

    if result:
        raise ReturnObject(model=result)

    raise ReturnObject(status=-1)


# 新增名片浏览数据
async def card_browse_model(data: CardBrowse, token: str):
    save_data = data.model_dump(exclude_unset=True)
    save_data["time"] = getMillisecond()

    if token:
        save_data["token"] = token

    # 查询名片的创建者
    createdToken = await findCardCreatedToken(data.cardId)
    if createdToken:
        save_data["createdToken"] = createdToken

    # 在cardBrowse集合添加一条记录 ，记录浏览时间 记录浏览的名片ID 记录浏览者的token
    result = await cardBrowse.insert_one(save_data)

    # 如果插入成功，返回成功
    if result.inserted_id:
        raise ReturnObject(status=0)
    raise ReturnObject(status=-1)


# 新增或更新名片点赞数据
async def card_like_model(data: CardLike, token: str):
    # 在cardLike集合更新一条记录 记录点赞的名片ID 记录卡片的创建者 记录点赞者的token 记录点赞时间 点赞状态 (true为点赞，false为取消点赞)
    current_time = getMillisecond()

    # 查询名片的创建者
    createdToken = await findCardCreatedToken(data.cardId)

    result = await cardLike.update_one(
        {"cardId": data.cardId, "token": token},
        {
            "$set": {
                "liked": data.liked,
                "time": current_time,
                "createdToken": createdToken,
            }
        },
        upsert=True,
    )

    # 如果更新成功，返回成功
    if result.modified_count == 1 or result.upserted_id:
        raise ReturnObject(status=0)
    raise ReturnObject(status=-1)


# 新增或更新名片收藏数据
async def card_collect_model(data: CardCollect, token: str):
    # 在cardCollect集合更新一条记录 记录收藏的名片ID 记录收藏者的token 记录收藏时间 收藏状态 (true为收藏，false为取消点赞)
    current_time = getMillisecond()

    # 查询名片的创建者
    createdToken = await findCardCreatedToken(data.cardId)

    result = await cardCollect.update_one(
        {"cardId": data.cardId, "token": token},
        {
            "$set": {
                "collected": data.collected,
                "time": current_time,
                "createdToken": createdToken,
            }
        },
        upsert=True,
    )

    # 如果更新成功，返回成功
    if result.modified_count == 1 or result.upserted_id:
        raise ReturnObject(status=0)
    raise ReturnObject(status=-1)


# 名片页面数据
async def card_page_model(data: Page, token: str):
    # 基础查询条件
    query = {"token": token, "deleted": False}

    # 计算总数
    total_count = await card.count_documents(query)

    # 执行分页查询
    cursor = card.find(query).skip((data.page - 1) * data.pageSize).limit(data.pageSize)

    # 将游标转换为列表
    resp = await cursor.to_list(length=data.pageSize)

    # 对数据进行格式化
    resp = formatModels(resp)

    # 构建响应模型
    model = {
        "data": resp,
        "total_count": total_count,
        "page": data.page,
        "pageSize": data.pageSize,
    }
    raise ReturnObject(model=model)


# 名片收藏页面数据
async def card_collect_page_model(data: Page, token: str):
    # 基础查询条件，查找用户收藏的名片
    query = {"token": token, "collected": True}

    # 查询用户收藏的 cardId 列表
    cursor = cardCollect.find(query)
    card_ids = await cursor.to_list(length=None)
    card_id_list = [str(card["cardId"]) for card in card_ids]

    # 如果没有收藏的名片，直接返回空列表
    if not card_id_list:
        model = {
            "data": [],
            "total_count": 0,
            "page": data.page,
            "pageSize": data.pageSize,
        }
        raise ReturnObject(model=model)

    # 在 card 集合中查询详细信息并分页
    card_query = {"_id": {"$in": [ObjectId(card_id) for card_id in card_id_list]}}
    total_count = await card.count_documents(card_query)

    # 执行分页查询
    cursor = (
        card.find(card_query).skip((data.page - 1) * data.pageSize).limit(data.pageSize)
    )

    # 将游标转换为列表
    resp = await cursor.to_list(length=data.pageSize)

    # 对数据进行格式化
    resp = formatModels(resp)

    # 构建响应模型
    model = {
        "data": resp,
        "total_count": total_count,
        "page": data.page,
        "pageSize": data.pageSize,
    }

    raise ReturnObject(model=model)


# 获取名片数据 (根据名片ID)
async def card_data_model(cardId: str, token: str):
    query = {"_id": ObjectId(cardId), "deleted": False}

    # 查询名片详细信息
    result = await card.find_one(query)

    if not result:
        raise ReturnObject(status=-1, message="名片不存在")

    query = {"cardId": cardId}  # 基础查询条件

    like_query = {**query, "liked": True}  # 点赞查询条件
    collect_query = {**query, "collected": True}  # 收藏查询条件

    # 获取卡片的点赞数量
    like_count = await cardLike.count_documents(like_query)
    result["likeCount"] = like_count

    # 获取卡片的收藏数量
    collect_count = await cardCollect.count_documents(collect_query)
    result["collectCount"] = collect_count

    # 获取卡片的浏览数量
    browse_count = await cardBrowse.count_documents(query)
    result["browseCount"] = browse_count

    if token:
        # 查询用户是否点赞了该名片
        like_query = {**like_query, "token": token}
        like_result = await cardLike.find_one(like_query)
        # 添加点赞状态到结果中
        result["liked"] = like_result is not None

        # 查询用户是否收藏了该名片
        collect_query = {**collect_query, "token": token}
        collect_result = await cardCollect.find_one(collect_query)
        # 添加收藏状态到结果中
        result["collected"] = collect_result is not None

    raise ReturnObject(model=result)
