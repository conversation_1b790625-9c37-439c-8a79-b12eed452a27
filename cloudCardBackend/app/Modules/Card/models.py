from pydantic import BaseModel


class Card(BaseModel):
    id: str = None
    name: str = None  # 名字
    company: str = None  # 公司
    email: str = None  # 邮箱
    phone: str = None  # 电话
    address: str = None  # 地址
    description: str = None  # 描述
    position: str = None  # 职位
    weChat: str = None  # 微信
    avatar: str = None  # 头像
    qrCode: str = None  # 微信二维码
    album: list = []  # 相册
    createdTime: int = None  # 创建时间
    updatedTime: int = None  # 更新时间
    deleted: bool = False  # 是否删除
    deletedTime: int = None  # 删除时间


class CardLike(BaseModel):
    id: str = None
    cardId: str = None  # 名片ID
    liked: bool = True  # 是否点赞
    time: int = None  # 点赞时间
    token: str = None  # 点赞人


class CardCollect(BaseModel):
    id: str = None
    cardId: str = None  # 名片ID
    collected: bool = True  # 是否收藏
    time: int = None  # 收藏时间
    token: str = None  # 收藏人


class CardBrowse(BaseModel):
    id: str = None
    cardId: str = None  # 名片ID
    time: int = None  # 浏览时间
    token: str = None  # 浏览人


class Page(BaseModel):
    page: int = 1  # 页码
    pageSize: int = 10  # 每页条数
