from fastapi import APIRouter, HTTPException, Header
from pydantic import BaseModel

from app.Modules.Card.services import (
    card_browse_model,
    card_collect_model,
    card_collect_page_model,
    card_data_model,
    card_like_model,
    card_page_model,
    card_save_model,
)
from app.Modules.Card.models import Card, CardBrowse, CardCollect, CardLike, Page
from app.Common.utils import ReturnObject, hasLogin


cardRouter = APIRouter()


# 名片浏览
@cardRouter.post("/browse")
async def card_delete(data: CardBrowse, token: str = Header(None)):
    try:
        if not data.cardId:
            raise ReturnObject(status=-1, message="名片ID不能为空")
        await card_browse_model(data, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 点赞名片
@cardRouter.post("/like")
async def card_delete(data: CardLike, token: str = Header(None)):
    try:
        if not data.cardId:
            raise ReturnObject(status=-1, message="名片ID不能为空")
        await hasLogin(token)
        await card_like_model(data, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 收藏名片
@cardRouter.post("/collect")
async def card_delete(data: CardCollect, token: str = Header(None)):
    try:
        if not data.cardId:
            raise ReturnObject(status=-1, message="名片ID不能为空")
        await hasLogin(token)
        await card_collect_model(data, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 保存名片
@cardRouter.post("/save")
async def card_create(data: Card, token: str = Header(None)):
    try:
        await hasLogin(token)
        await card_save_model(data, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 名片页面
@cardRouter.post("/page")
async def card_page(data: Page, token: str = Header(None)):
    try:
        await hasLogin(token)
        await card_page_model(data, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 收藏名片页面
@cardRouter.post("/collect/page")
async def card_page(data: Page, token: str = Header(None)):
    try:
        await hasLogin(token)
        await card_collect_page_model(data, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


class CardData(BaseModel):
    cardId: str


# 获取名片数据
@cardRouter.post("/data")
async def card_data(data: CardData, token: str = Header(None)):
    try:
        if not data.cardId:
            raise ReturnObject(status=-1, message="名片ID不能为空")
        await card_data_model(data.cardId, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
