import os
from fastapi import APIRouter, File, HTTPException, Header, UploadFile
from fastapi.responses import PlainTextResponse

from app.Modules.User.models import User, WxAuthCode
from app.Modules.User.services import auth_login_model, user_info_model, user_save_model
from app.Common.utils import ReturnObject, hasLogin, sha256_hash


userRouter = APIRouter()


# 获取用户信息
@userRouter.post("/info")
async def user_info(token: str = Header(None)):
    try:
        await hasLogin(token)
        await user_info_model(token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 登录
@userRouter.post("/login")
async def auth_login(data: WxAuthCode):
    try:
        await auth_login_model(data)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 上传头像
@userRouter.post("/avatarUpload")
async def avatar_upload(
    token: str = Header(None),  # 从请求头中获取 token
    file: UploadFile = File(...),  # 上传的文件
):
    try:
        await hasLogin(token, False)

        # 检查文件类型
        if not file.content_type.startswith("image"):
            raise ReturnObject(status=-1, message="请上传图片文件")

        # 读取文件内容
        file_content = await file.read()

        MAX_FILE_SIZE = 2 * 1024 * 1024  # 2MB

        # 获取文件大小
        file_size = len(file_content)
        if file_size > MAX_FILE_SIZE:
            raise ReturnObject(status=-1, message="图片大小不能超过 2MB")

        # 计算 token 的 SHA-256 哈希值
        token_sha_256 = sha256_hash(token)

        # 设置保存文件的路径
        file_path = os.path.join("assets/avatars", f"{token_sha_256}.jpeg")

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 保存文件到本地
        with open(file_path, "wb") as f:
            f.write(file_content)

        return PlainTextResponse(file_path)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 保存用户信息
@userRouter.post("/save")
async def user_save(data: User, token: str = Header(None)):
    try:
        await hasLogin(token, False)
        await user_save_model(data, token)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
