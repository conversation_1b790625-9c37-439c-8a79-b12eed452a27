import asyncio
from pymongo import ReturnDocument

from app.Common.db import user, card, cardCollect, cardLike, cardBrowse
from httpx import AsyncClient

from app.Common.utils import ReturnObject
from app.Modules.User.models import User, WxAuthCode


# 微信登录授权
async def auth_login_model(data: WxAuthCode):
    url = "https://api.weixin.qq.com/sns/jscode2session"
    params = {
        "appid": "wxa83883269227936a",
        "secret": "8180278855a09302667071c51a97bf69",
        "js_code": data.code,
        "grant_type": "authorization_code",
    }

    async with AsyncClient() as client:
        response = await client.get(url, params=params)

    if response.status_code != 200:
        raise ReturnObject(status=-1, message="微信登录失败")

    model = {"token": response.json()["openid"]}
    raise ReturnObject(model=model, notReturnToken=False)


# 用户保存
async def user_save_model(data: User, token: str):
    save_data = data.model_dump(exclude_unset=True)
    # 查询token是否存在，有就更新，没有就插入
    result = await user.find_one_and_update(
        {"token": token},
        {"$set": save_data},
        upsert=True,
        return_document=ReturnDocument.AFTER,
    )

    if result:
        raise ReturnObject(model=result, notReturnToken=False)

    raise ReturnObject(status=-1)


# 用户查询
async def user_info_model(token: str):
    find_result = await user.find_one({"token": token})
    if find_result:
        # 查询我的名片数量
        card_count_future = card.count_documents({"token": token, "deleted": False})
        # 查询收藏名片数量
        collect_count_future = cardCollect.count_documents(
            {"token": token, "collected": True}
        )
        # 查询点赞名片数量
        like_count_future = cardLike.count_documents({"token": token, "liked": True})
        # 查询我的名片被收藏数量
        collect_card_count_future = cardCollect.count_documents(
            {"createdToken": token, "collected": True}
        )
        # 查询我的名片被点赞数量
        like_card_count_future = cardLike.count_documents(
            {"createdToken": token, "liked": True}
        )
        # 查询我的名片被浏览数量
        browse_count_future = cardBrowse.count_documents({"createdToken": token})

        (
            card_count,
            collect_count,
            like_count,
            collect_card_count,
            like_card_count,
            browse_count,
        ) = await asyncio.gather(
            card_count_future,
            collect_count_future,
            like_count_future,
            collect_card_count_future,
            like_card_count_future,
            browse_count_future,
        )
        find_result = {
            **find_result,
            "cardCount": card_count,
            "collectCardCount": collect_count,
            "likeCardCount": like_count,
            "cardCollectCount": collect_card_count,
            "cardLikeCount": like_card_count,
            "cardBrowseCount": browse_count,
        }
        raise ReturnObject(model=find_result, notReturnToken=False)

    raise ReturnObject(status=-1, message="用户不存在")
