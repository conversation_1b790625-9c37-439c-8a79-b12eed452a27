from app.Common.db import carousel
from app.Modules.Carousel.models import CarouselQuery
from app.Common.utils import ReturnObject


# 获取轮播图列表
async def carousel_list_model(query: CarouselQuery):
    # 构建查询条件 - 只获取启用状态的轮播图
    filter_query = {"status": 1}
    
    # 查询数据 - 按照排序字段升序排列
    cursor = carousel.find(filter_query).sort("sort", 1).limit(query.limit)
    items = await cursor.to_list(length=query.limit)
    
    # 返回结果
    raise ReturnObject(model=items)
