from fastapi import APIRouter, HTTPException, Header

from app.Modules.Carousel.models import CarouselQuery
from app.Modules.Carousel.services import carousel_list_model
from app.Common.utils import ReturnObject


carouselRouter = APIRouter()


# 获取轮播图列表
@carouselRouter.post("/list")
async def carousel_list(query: CarouselQuery = CarouselQuery()):
    try:
        await carousel_list_model(query)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
