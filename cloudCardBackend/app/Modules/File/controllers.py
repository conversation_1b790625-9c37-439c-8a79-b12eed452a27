import os
import base64
import mimetypes
import oss2
from fastapi import APIRouter, File, HTTPException, Header, UploadFile, Body
from fastapi.responses import FileResponse, PlainTextResponse, RedirectResponse
from pymongo import ReturnDocument
from pydantic import BaseModel
from app.Common.utils import ReturnObject, getMillisecond, getUUID, hasLogin
from app.Common.db import file as fileDB
from app.Common.base_controller import getAdminInfo

fileRouter = APIRouter()
resourceRouter = APIRouter()


# Base64文件上传请求模型
class Base64FileUpload(BaseModel):
    data: str  # base64编码的文件数据（不包含data:image/png;base64,前缀）
    fileName: str  # 文件名


# 文件上传
@fileRouter.post("/upload")
async def file_upload(token: str = Header(None), file: UploadFile = File(...)):
    try:
        await hasLogin(token)

        if not file.content_type.startswith("image"):
            raise ReturnObject(status=-1, message="不支持的文件类型")

        MAX_FILE_SIZE = 2 * 1024 * 1024  # 2MB

        # 读取文件内容
        file_content = await file.read()

        # 获取文件大小
        file_size = len(file_content)
        if file_size > MAX_FILE_SIZE:
            raise ReturnObject(status=-1, message="文件大小不能超过 2MB")

        # 计算 token 的 SHA-256 哈希值
        name = getUUID()

        # 设置保存文件的路径
        file_path = os.path.join(
            "assets/files", f"{name}.{file.content_type.split('/')[1]}"
        )

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 保存文件到本地
        with open(file_path, "wb") as f:
            f.write(file_content)

        # 保存文件数据
        current_time = getMillisecond()
        save_data = {
            "name": file.filename,
            "path": file_path,
            "size": file_size,
            "token": token,
            "createdTime": current_time,
            "type": file.content_type,
            "fileId": name,
        }
        result = await fileDB.insert_one(save_data)
        if result.inserted_id:
            raise ReturnObject(
                model={"fileId": name, "size": file_size}, message="Success"
            )

        raise ReturnObject(status=-1, message="Error")
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 上传 Base64 文件到 OSS
@fileRouter.post("/upload_base64")
async def file_upload_base64(
    data: Base64FileUpload = Body(...), authorization: str = Header(None)
):
    try:
        # 验证管理员身份
        admin_info = await getAdminInfo(authorization)

        # 解码base64数据
        try:
            file_content = base64.b64decode(data.data)
        except Exception:
            raise ReturnObject(status=-1, message="无效的base64数据")

        file_size = len(file_content)
        MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

        if file_size > MAX_FILE_SIZE:
            raise ReturnObject(status=-1, message="文件大小不能超过 10MB")

        # 获取文件扩展名
        file_extension = ""
        if "." in data.fileName:
            file_extension = data.fileName.split(".")[-1].lower()

        # 推断扩展名（如无）
        if not file_extension:
            if file_content.startswith(b"\x89PNG"):
                file_extension = "png"
            elif file_content.startswith(b"\xff\xd8\xff"):
                file_extension = "jpg"
            elif file_content.startswith(b"GIF"):
                file_extension = "gif"
            elif file_content.startswith(
                b"\x00\x00\x00\x20ftypheic"
            ) or file_content.startswith(b"\x00\x00\x00\x18ftypheic"):
                file_extension = "heic"
            else:
                file_extension = "bin"

        file_id = getUUID()
        object_key = f"uploads/{file_id}.{file_extension}"

        # 初始化 OSS 客户端
        auth = oss2.Auth("LTAI5tK96hZz3M6F6ZS12Eyy", "******************************")
        bucket = oss2.Bucket(auth, f"https://oss-cn-beijing.aliyuncs.com", "yinshiwl01")

        # 上传到 OSS
        bucket.put_object(object_key, file_content)

        # 构建可访问的 URL（仅公共读）
        oss_file_url = f"https://yinshiwl01.oss-cn-beijing.aliyuncs.com/{object_key}"

        # 推断 MIME 类型
        mime_type, _ = mimetypes.guess_type(data.fileName)
        if not mime_type:
            if file_extension in ["jpg", "jpeg"]:
                mime_type = "image/jpeg"
            elif file_extension == "png":
                mime_type = "image/png"
            elif file_extension == "gif":
                mime_type = "image/gif"
            elif file_extension == "heic":
                mime_type = "image/heic"
            else:
                mime_type = "application/octet-stream"

        current_time = getMillisecond()
        save_data = {
            "name": data.fileName,
            "path": oss_file_url,
            "size": file_size,
            "createdTime": current_time,
            "type": mime_type,
            "fileId": file_id,
            "createdById": admin_info.get("id"),
            "createdByName": admin_info.get("username"),
        }

        result = await fileDB.insert_one(save_data)
        if result.inserted_id:
            raise ReturnObject(
                model={
                    "id": file_id,
                    "fileId": file_id,
                    "size": file_size,
                    "url": oss_file_url,
                },
                message="Success",
            )

        raise ReturnObject(status=-1, message="文件保存失败")

    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@fileRouter.get("/{fileId}")
async def file(fileId: str, token: str = Header(None)):
    try:
        current_time = getMillisecond()
        result = await fileDB.find_one_and_update(
            {"fileId": fileId},
            {"$set": {"time": current_time}},
            return_document=ReturnDocument.AFTER,
        )
        if result:
            path = result["path"]
            return FileResponse(path)
        raise HTTPException(status_code=404, detail="文件不存在")
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@resourceRouter.get("/image/{fileId}")
async def file(fileId: str, token: str = Header(None)):
    try:
        current_time = getMillisecond()
        result = await fileDB.find_one_and_update(
            {"fileId": fileId},
            {"$set": {"time": current_time}},
            return_document=ReturnDocument.AFTER,
        )
        if result:
            oss_url = result["path"]
            return RedirectResponse(url=oss_url)
        raise HTTPException(status_code=404, detail="文件不存在")
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
