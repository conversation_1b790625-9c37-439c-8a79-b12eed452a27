from fastapi import APIRouter, HTTPException, Header

from app.Admin.User.models import (
    UserEntity,
    UserPageQuery,
)

from app.Common.base_controller import BaseController
from app.Admin.User.services import UserService

userAdminRouter = APIRouter()
# 创建服务实例
user_service = UserService()

# 创建控制器
user_controller = BaseController(
    router=userAdminRouter,
    service=user_service,
    entity_class=UserEntity,
    page_query_class=UserPageQuery,
    prefix="",
    tags=["用户管理"],
)
