from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.Common.base_models import BaseEntity, PageQuery


# 实体模型
class UserEntity(BaseEntity):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    phone: Optional[str] = Field(None, description="手机号")
    email: Optional[str] = Field(None, description="邮箱")
    status: int = Field(1, description="状态: 0-禁用, 1-启用")


# 分页查询参数
class UserPageQuery(PageQuery):
    username: Optional[str] = Field(None, description="用户名")
    phone: Optional[str] = Field(None, description="手机号")
    email: Optional[str] = Field(None, description="邮箱")
    status: Optional[int] = Field(None, description="状态: 0-禁用, 1-启用")
