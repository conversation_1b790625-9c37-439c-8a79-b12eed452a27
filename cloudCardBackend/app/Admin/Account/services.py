import hashlib
from bson import ObjectId
from app.Common.base_service import BaseService
from app.Common.db import account
from app.Admin.Account.models import AccountEntity, AccountLogin
from app.Common.utils import ReturnObject, getMillisecond, create_access_token, verify_access_token


class AccountService(BaseService):

    def __init__(self):
        super().__init__(account, AccountEntity)

    # 登录账户
    async def account_login_model(self, account_data: AccountLogin):
        # 查询账户是否存在
        existing_account = await account.find_one({"username": account_data.username})
        if not existing_account:
            raise ReturnObject(status=404, message="账户不存在")

        # 验证密码
        hashed_password = hashlib.md5(account_data.password.encode()).hexdigest()
        if existing_account.get("password") != hashed_password:
            raise ReturnObject(status=-1, message="密码错误")

        # 生成 JWT token
        token_data = {
            "sub": str(existing_account["_id"]),
            "username": existing_account["username"],
        }
        access_token = create_access_token(token_data)

        # 更新登录时间和token
        update_data = {
            "accessToken": access_token,
            "lastLoginAt": getMillisecond(),
        }

        # 更新账户信息
        result = await account.find_one_and_update(
            {"_id": existing_account["_id"]},
            {"$set": update_data},
            return_document=True,
        )

        if result:
            raise ReturnObject(model=result, message="登录成功", notReturnToken=False)

        raise ReturnObject(status=-1, message="登录失败")

    # 获取用户信息
    async def account_info_model(self, authorization: str = None):
        # 验证 token
        payload = verify_access_token(authorization)

        # 获取用户ID
        user_id = payload.get("sub")
        if not user_id:
            raise ReturnObject(status=401, message="无效的Token")

        try:
            object_id = ObjectId(user_id)
        except:
            raise ReturnObject(status=401, message="无效的用户ID")

        # 查询用户信息
        user = await account.find_one({"_id": object_id})
        if not user:
            raise ReturnObject(status=401, message="用户不存在")

        raise ReturnObject(model=user, notReturnToken=False)
