from fastapi import APIRouter, HTTPException, Header

from app.Admin.Account.models import (
    AccountEntity,
    AccountLogin,
    AccountPageQuery,
)

from app.Common.utils import ReturnObject
from app.Common.base_controller import BaseController
from app.Admin.Account.services import AccountService


accountAdminRouter = APIRouter()


# 创建服务实例
account_service = AccountService()

# 创建控制器
account_controller = BaseController(
    router=accountAdminRouter,
    service=account_service,
    entity_class=AccountEntity,
    page_query_class=AccountPageQuery,
    prefix="",
    tags=["账户管理"],
)


# 登录账户
@accountAdminRouter.post("/login", tags=["账户管理"])
async def account_login(account: AccountLogin):
    try:
        await account_service.account_login_model(account)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 获取用户信息
@accountAdminRouter.post("/info", tags=["账户管理"])
async def account_info(authorization: str = Header(None)):
    try:
        await account_service.account_info_model(authorization)
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
