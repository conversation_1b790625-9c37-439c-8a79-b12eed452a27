from app.Common.utils import ReturnObject
from fastapi import APIRouter

from app.Admin.Love.models import (
    LoveEntity,
    LovePageQuery,
)

from app.Common.base_controller import BaseController
from app.Admin.Love.services import LoveService

loveRouter = APIRouter()
# 创建服务实例
love_service = LoveService()

# 创建控制器
love_controller = BaseController(
    router=loveRouter,
    service=love_service,
    entity_class=LoveEntity,
    page_query_class=LovePageQuery,
    prefix="",
    tags=["恋爱清单"],
)
