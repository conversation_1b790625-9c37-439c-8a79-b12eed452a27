from typing import Dict
from app.Common.base_service import BaseService
from app.Common.db import love, loveComment
from app.Admin.Love.models import LoveEntity
from app.Common.utils import ReturnObject
from app.Common.base_models import PageQuery


class LoveService(BaseService):

    def __init__(self):
        super().__init__(love, LoveEntity)

    async def page(
        self, query: PageQuery, admin_info: dict = None, is_list: bool = False
    ) -> Dict:
        """
        重写分页查询方法，添加评论数量统计

        Args:
            query: 分页查询参数
            admin_info: 管理员信息
            is_list: 是否是列表查询

        Returns:
            分页结果，包含评论数量
        """
        # 先调用父类的分页查询方法
        try:
            await super().page(query, admin_info, is_list)
        except ReturnObject as result:
            # 获取查询结果
            if hasattr(result, "page") and result.page:
                # 分页查询结果
                items = result.page.get("content", [])

                # 为每个love记录添加评论数量
                for item in items:
                    if item.get("id"):
                        # 统计该love记录的评论数量
                        comment_count = await loveComment.count_documents(
                            {"loveId": str(item["id"]), "deleted": False}
                        )
                        y_un_read_count = await loveComment.count_documents(
                            {
                                "loveId": str(item["id"]),
                                "deleted": False,
                                "yRead": False,
                            }
                        )
                        x_un_read_count = await loveComment.count_documents(
                            {
                                "loveId": str(item["id"]),
                                "deleted": False,
                                "xRead": False,
                            }
                        )
                        item["yUnReadCount"] = y_un_read_count
                        item["xUnReadCount"] = x_un_read_count
                        item["commentCount"] = comment_count

                # 返回带有评论数量的结果
                raise ReturnObject(page=result.page)

            else:
                # 如果没有数据，直接返回原结果
                raise result
