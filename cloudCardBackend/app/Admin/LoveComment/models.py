from pydantic import Field
from typing import Optional
from app.Common.base_models import BaseEntity, PageQuery


# 实体模型
class LoveCommentEntity(BaseEntity):
    loveId: Optional[str] = Field(..., description="恋爱清单ID")
    content: Optional[str] = Field(..., description="评论内容")
    name: Optional[str] = Field(..., description="评论人: 小徐, 小张")
    xRead: Optional[bool] = Field(False, description="小张是否已读")
    yRead: Optional[bool] = Field(False, description="小徐是否已读")


# 分页查询参数
class LoveCommentPageQuery(PageQuery):
    loveId: Optional[str] = Field(..., description="恋爱清单ID", query_type="exact")
