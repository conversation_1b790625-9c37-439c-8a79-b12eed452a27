from fastapi import APIRouter

from app.Admin.LoveComment.models import (
    LoveCommentEntity,
    LoveCommentPageQuery,
)

from app.Common.base_controller import BaseController
from app.Admin.LoveComment.services import LoveCommentService

loveCommentRouter = APIRouter()
# 创建服务实例
loveComment_service = LoveCommentService()

# 创建控制器
loveComment_controller = BaseController(
    router=loveCommentRouter,
    service=loveComment_service,
    entity_class=LoveCommentEntity,
    page_query_class=LoveCommentPageQuery,
    prefix="",
    tags=["恋爱评论"],
)
