import asyncio
from app.Common.wechat import get_game


async def main():
    # 循环
    offset = ""
    i = 0
    while True:
        print(f"第{i + 1}次抓取")
        data = await get_game(offset)
        i += 1
        if data.errcode != 0:
            print(f"抓取失败，errcode: {data.errcode}, errmsg: {data.errmsg}")
            break
        if not data.has_next:
            break
        offset = data.next_offset

        # 保存数据
        with open(f"game_{i}.json", "w") as f:
            f.write(data.json())
    print("抓取完成")


if __name__ == "__main__":
    asyncio.run(main())
